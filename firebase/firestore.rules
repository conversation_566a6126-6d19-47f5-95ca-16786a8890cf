rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if a value exists
    function exists(value) {
      return value != null;
    }

    // Helper function to validate email format
    function isValidEmail(email) {
      return email.matches('^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');
    }

    // Email signups collection
    match /email_signups/{document} {
      allow read: if false; // No client-side reading of email list
      allow create: if exists(request.resource.data.email)
                   && isValidEmail(request.resource.data.email)
                   && request.resource.data.keys().hasAll(['email', 'timestamp', 'status'])
                   && request.resource.data.status == 'pending';
      allow update, delete: if false;
    }

    // Feature votes collection
    match /feature_votes/{featureId} {
      allow read: if true; // Anyone can read vote counts
      allow create: if exists(request.resource.data.votes)
                   && request.resource.data.votes is number
                   && request.resource.data.votes >= 0;
      allow update: if exists(request.resource.data.votes)
                   && request.resource.data.votes is number
                   && request.resource.data.votes >= 0
                   && request.resource.data.diff(resource.data).affectedKeys().hasOnly(['votes']);
      allow delete: if false;
    }

    // Contact form submissions
    match /contact_submissions/{document} {
      allow read: if false; // No client-side reading of submissions
      allow create: if exists(request.resource.data.email)
                   && isValidEmail(request.resource.data.email)
                   && exists(request.resource.data.message)
                   && exists(request.resource.data.name)
                   && request.resource.data.keys().hasAll(['email', 'message', 'name', 'timestamp'])
                   && request.resource.data.message.size() <= 1000; // Limit message size
      allow update, delete: if false;
    }

    // Unsubscribe tokens
    match /unsubscribe_tokens/{document} {
      allow read: if exists(resource.data.token)
                 && request.query.limit <= 1; // Only allow single token lookup
      allow create: if false; // Only created by backend
      allow update: if exists(resource.data.token)
                   && request.resource.data.diff(resource.data).affectedKeys().hasOnly(['used', 'used_at']);
      allow delete: if false;
    }

    // Discount codes
    match /discount_codes/{document} {
      allow read: if false; // No client-side reading of discount codes
      allow write: if false; // Only managed by backend
    }

    // Community strategies - read-only for all authenticated users
    match /communityStrategies/{strategyId} {
      allow read: if request.auth != null;
      allow write: if false; // Only allow server-side writes
    }

    // Privileged emails - read-only for server-side functions
    match /privilegedEmails/{emailId} {
      allow read: if false; // Only allow server-side reads
      allow write: if false; // Only allow server-side writes
    }

    // User data and strategies
    match /users/{userId} {
      // Allow users to read and write their own data
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow users to read and write their own strategies
      match /strategies/{strategyId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // Allow users to read and write their own submitted strategies
      match /submittedStrategies/{strategyId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;

        // Allow access to trade history for the strategy
        match /tradeHistory/{tradeId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }
      }
    }

    // Default deny all other collections
    match /{document=**} {
      allow read, write: if false;
    }
    
    // Allow users to read and write their own data in development
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      match /{collection}/{docId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
        
        match /{nestedCollection}/{nestedDocId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }
      }
    }
  }
}