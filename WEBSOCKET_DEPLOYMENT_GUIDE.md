# 🚀 Oryn WebSocket Service - Complete Deployment Guide

This guide will walk you through deploying the Oryn WebSocket service to Google Cloud with full security implementation.

## 📋 Prerequisites

1. **Google Cloud Account** with billing enabled
2. **Google Cloud SDK** installed and authenticated
3. **Docker** installed and running
4. **Polygon.io API Key**
5. **Project permissions** for Cloud Run and Container Registry

## 🔐 Step 1: Generate API Keys

Generate secure API keys for authentication:

```bash
# Generate API keys
./scripts/generate_api_keys.sh

# Source the generated keys
source scripts/websocket_api_keys.env
```

This creates:
- **Frontend API Key**: For web application connections
- **Trade-bot API Key**: For trade-bot service connections  
- **Admin API Key**: For administrative access

## 🌐 Step 2: Set Environment Variables

Set your Polygon.io API key:

```bash
export POLYGON_API_KEY="your_polygon_api_key_here"
```

Verify all required variables are set:

```bash
echo "Polygon API Key: ${POLYGON_API_KEY:0:10}..."
echo "WebSocket API Keys: ${WEBSOCKET_API_KEYS:0:20}..."
echo "Allowed Origins: $ALLOWED_ORIGINS"
```

## 🚀 Step 3: Deploy to Production

Deploy the WebSocket service with security:

```bash
./scripts/deploy_websocket_production.sh
```

This script will:
1. Build and push Docker image to Google Container Registry
2. Deploy to Cloud Run with security configurations
3. Update client configurations automatically
4. Test the deployment
5. Provide next steps

## 🔧 Step 4: Configure Clients

### Frontend Configuration

Create `frontend/.env.local`:

```bash
# Use the frontend API key from the generated keys
REACT_APP_WEBSOCKET_API_KEY=b24735c7f1ccbff6de39328ebbfdbf4e9e2999699d062119915575184f576a4b
```

### Trade-bot Configuration

Update `trade-bot-service/.env`:

```bash
# Production WebSocket URL (will be updated automatically by deployment script)
WEBSOCKET_SERVICE_URL=wss://oryn-websocket-service-xxx.run.app/ws

# Trade-bot API key
WEBSOCKET_API_KEY=689963d507a588a391d7f634a8da6e68f4dd755cafbcf234b77ffd541f35a09c
```

## 🧪 Step 5: Test the Deployment

### Test WebSocket Connection

Install wscat for testing:
```bash
npm install -g wscat
```

Test with API key:
```bash
wscat -c "wss://your-service-url/ws?api_key=your_api_key"
```

### Test Health Endpoints

```bash
# Health check
curl https://your-service-url/health

# Service status
curl https://your-service-url/status
```

## 📊 Step 6: Monitor the Service

### View Logs
```bash
gcloud run logs tail --service=oryn-websocket-service --region=us-central1
```

### Monitor Metrics
```bash
# Check service status
gcloud run services describe oryn-websocket-service --region=us-central1

# Monitor authentication failures
gcloud run logs read --service=oryn-websocket-service --filter="Authentication failed"
```

## 🔄 Step 7: Team Configuration

### For Development (Local)
```bash
# Configure for local development
./scripts/configure_environment.sh development
```

### For Production
```bash
# Configure for production
./scripts/configure_environment.sh production
```

## 🛡️ Security Features Implemented

### Multi-Layer Authentication
1. **API Key Authentication**: Unique keys for each client type
2. **Origin Validation**: Only allowed domains can connect
3. **User-Agent Authentication**: Trade-bot auto-authentication
4. **Development Mode**: Localhost bypass for development

### Security Configuration
- **Allowed Origins**: `https://oryntrade.com`, `http://localhost:3000`, `http://127.0.0.1:4000`
- **API Keys**: 64-character secure random keys
- **Connection Limits**: 1000 concurrent connections per instance
- **Auto-scaling**: 1-20 instances based on demand

## 📈 Scaling Configuration

Current settings:
- **Min Instances**: 1 (always warm)
- **Max Instances**: 20 (auto-scaling)
- **CPU**: 2 vCPU per instance
- **Memory**: 2 GiB per instance
- **Concurrency**: 1000 connections per instance

To adjust scaling:
```bash
gcloud run services update oryn-websocket-service \
  --max-instances=50 \
  --region=us-central1
```

## 💰 Cost Estimation

**Monthly costs** (approximate):
- **Base service**: $20-30/month (1 instance always running)
- **Scaling**: $5-10 per additional instance hour
- **Data transfer**: $0.12 per GB
- **Total estimated**: $30-60/month for moderate usage

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Failed**
   ```bash
   # Check API keys are set
   echo $WEBSOCKET_API_KEYS
   
   # Verify client is using correct key
   wscat -c "wss://your-service-url/ws?api_key=correct_key"
   ```

2. **Service Not Responding**
   ```bash
   # Check service status
   gcloud run services describe oryn-websocket-service --region=us-central1
   
   # View recent logs
   gcloud run logs read --service=oryn-websocket-service --limit=50
   ```

3. **High Latency**
   ```bash
   # Increase min instances
   gcloud run services update oryn-websocket-service \
     --min-instances=2 \
     --region=us-central1
   ```

### Emergency Procedures

**If API Key is Compromised:**
1. Generate new keys: `./scripts/generate_api_keys.sh`
2. Update environment: `source scripts/websocket_api_keys.env`
3. Redeploy service: `./scripts/deploy_websocket_production.sh`
4. Update all clients with new keys

**If Service is Under Attack:**
1. Restrict origins: `export ALLOWED_ORIGINS="https://oryntrade.com"`
2. Redeploy with restrictions
3. Monitor logs for suspicious activity

## ✅ Deployment Checklist

- [ ] Google Cloud SDK authenticated
- [ ] Polygon.io API key set
- [ ] API keys generated and sourced
- [ ] Service deployed successfully
- [ ] Health checks passing
- [ ] Frontend configured with API key
- [ ] Trade-bot configured with API key
- [ ] WebSocket connections tested
- [ ] Monitoring set up
- [ ] Team notified of new URLs

## 🎯 Success Criteria

Your deployment is successful when:
- ✅ Health endpoint returns 200 OK
- ✅ WebSocket connections work with API keys
- ✅ Frontend can connect and receive data
- ✅ Trade-bot can connect and receive data
- ✅ Authentication failures are logged
- ✅ Service auto-scales under load

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review service logs for error messages
3. Verify all environment variables are set correctly
4. Test with wscat to isolate connection issues

Your WebSocket service is now ready for production use with enterprise-grade security! 🎉
