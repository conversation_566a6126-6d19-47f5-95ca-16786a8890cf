#!/bin/bash

# Oryn Environment Configuration Script
# This script helps switch between development and production configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_CONFIG="frontend/src/config.js"
TRADEBOT_ENV="trade-bot-service/.env"
WEBSOCKET_SERVICE_URL_PROD="wss://oryn-websocket-service-ihjc6tjxia-uc.a.run.app/ws"
WEBSOCKET_SERVICE_URL_DEV="ws://localhost:8081/ws"

show_usage() {
    echo "Usage: $0 [development|production|status]"
    echo ""
    echo "Commands:"
    echo "  development  - Configure for local development"
    echo "  production   - Configure for production deployment"
    echo "  status       - Show current configuration"
    echo ""
}

show_status() {
    echo -e "${BLUE}🔍 Current Configuration Status${NC}"
    echo "=================================="
    
    # Check frontend config
    if grep -q "USE_FIREBASE_EMULATOR = true" "$FRONTEND_CONFIG" 2>/dev/null; then
        echo -e "Frontend: ${YELLOW}Development${NC} (Firebase Emulator enabled)"
    elif grep -q "USE_FIREBASE_EMULATOR = false" "$FRONTEND_CONFIG" 2>/dev/null; then
        echo -e "Frontend: ${GREEN}Production${NC} (Firebase Emulator disabled)"
    else
        echo -e "Frontend: ${RED}Unknown${NC} (Config file not found or malformed)"
    fi
    
    # Check trade-bot config
    if [ -f "$TRADEBOT_ENV" ]; then
        if grep -q "localhost:8081" "$TRADEBOT_ENV"; then
            echo -e "Trade-bot: ${YELLOW}Development${NC} (localhost WebSocket)"
        elif grep -q "oryn-websocket-service" "$TRADEBOT_ENV"; then
            echo -e "Trade-bot: ${GREEN}Production${NC} (Cloud Run WebSocket)"
        else
            echo -e "Trade-bot: ${RED}Unknown${NC} (WebSocket URL not configured)"
        fi
    else
        echo -e "Trade-bot: ${RED}Not configured${NC} (.env file not found)"
    fi
    
    echo "=================================="
}

configure_development() {
    echo -e "${YELLOW}🔧 Configuring for Development Environment${NC}"
    
    # Update frontend config
    if [ -f "$FRONTEND_CONFIG" ]; then
        sed -i.bak 's/USE_FIREBASE_EMULATOR = false/USE_FIREBASE_EMULATOR = true/g' "$FRONTEND_CONFIG"
        echo -e "${GREEN}✅ Frontend configured for development${NC}"
    else
        echo -e "${RED}❌ Frontend config file not found: $FRONTEND_CONFIG${NC}"
    fi
    
    # Update trade-bot config
    if [ -f "$TRADEBOT_ENV" ]; then
        # Comment out production URL and uncomment development URL
        sed -i.bak "s|^WEBSOCKET_SERVICE_URL=.*oryn-websocket-service.*|# WEBSOCKET_SERVICE_URL=$WEBSOCKET_SERVICE_URL_PROD  # Production|g" "$TRADEBOT_ENV"
        sed -i.bak "s|^# WEBSOCKET_SERVICE_URL=.*localhost.*|WEBSOCKET_SERVICE_URL=$WEBSOCKET_SERVICE_URL_DEV  # Development|g" "$TRADEBOT_ENV"
        
        # If no WebSocket URL exists, add development one
        if ! grep -q "WEBSOCKET_SERVICE_URL=" "$TRADEBOT_ENV"; then
            echo "" >> "$TRADEBOT_ENV"
            echo "# WebSocket Service Configuration" >> "$TRADEBOT_ENV"
            echo "WEBSOCKET_SERVICE_URL=$WEBSOCKET_SERVICE_URL_DEV  # Development" >> "$TRADEBOT_ENV"
        fi
        
        echo -e "${GREEN}✅ Trade-bot configured for development${NC}"
    else
        echo -e "${YELLOW}⚠️  Trade-bot .env file not found, creating from example${NC}"
        if [ -f "trade-bot-service/.env.example" ]; then
            cp "trade-bot-service/.env.example" "$TRADEBOT_ENV"
            echo -e "${GREEN}✅ Created trade-bot .env file${NC}"
        fi
    fi
    
    echo -e "${GREEN}🎉 Development configuration complete!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Start the local WebSocket service: cd functions && python start_websocket_service.py"
    echo "2. Start the Firebase emulator: npm run emulator"
    echo "3. Start the frontend: npm run dev"
}

configure_production() {
    echo -e "${YELLOW}🔧 Configuring for Production Environment${NC}"
    
    # Update frontend config
    if [ -f "$FRONTEND_CONFIG" ]; then
        sed -i.bak 's/USE_FIREBASE_EMULATOR = true/USE_FIREBASE_EMULATOR = false/g' "$FRONTEND_CONFIG"
        echo -e "${GREEN}✅ Frontend configured for production${NC}"
    else
        echo -e "${RED}❌ Frontend config file not found: $FRONTEND_CONFIG${NC}"
    fi
    
    # Update trade-bot config
    if [ -f "$TRADEBOT_ENV" ]; then
        # Comment out development URL and uncomment production URL
        sed -i.bak "s|^WEBSOCKET_SERVICE_URL=.*localhost.*|# WEBSOCKET_SERVICE_URL=$WEBSOCKET_SERVICE_URL_DEV  # Development|g" "$TRADEBOT_ENV"
        sed -i.bak "s|^# WEBSOCKET_SERVICE_URL=.*oryn-websocket-service.*|WEBSOCKET_SERVICE_URL=$WEBSOCKET_SERVICE_URL_PROD  # Production|g" "$TRADEBOT_ENV"
        
        # If no production WebSocket URL exists, add it
        if ! grep -q "oryn-websocket-service" "$TRADEBOT_ENV"; then
            echo "" >> "$TRADEBOT_ENV"
            echo "# WebSocket Service Configuration" >> "$TRADEBOT_ENV"
            echo "WEBSOCKET_SERVICE_URL=$WEBSOCKET_SERVICE_URL_PROD  # Production" >> "$TRADEBOT_ENV"
        fi
        
        echo -e "${GREEN}✅ Trade-bot configured for production${NC}"
    else
        echo -e "${RED}❌ Trade-bot .env file not found: $TRADEBOT_ENV${NC}"
    fi
    
    echo -e "${GREEN}🎉 Production configuration complete!${NC}"
    echo ""
    echo "⚠️  Make sure the WebSocket service is deployed to Cloud Run!"
    echo "Deploy with: cd functions && ./deploy_to_cloud_run.sh"
}

# Main script logic
case "${1:-}" in
    "development"|"dev")
        configure_development
        ;;
    "production"|"prod")
        configure_production
        ;;
    "status")
        show_status
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
