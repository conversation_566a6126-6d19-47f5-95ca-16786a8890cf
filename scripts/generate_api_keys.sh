#!/bin/bash

# Generate secure API keys for Oryn WebSocket service
# This script creates unique API keys for different client types

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔑 Generating Secure API Keys for Oryn WebSocket Service${NC}"
echo "=================================================="

# Generate unique API keys for different clients
FRONTEND_API_KEY=$(openssl rand -hex 32)
TRADEBOT_API_KEY=$(openssl rand -hex 32)
ADMIN_API_KEY=$(openssl rand -hex 32)

# Combine all keys for the service
WEBSOCKET_API_KEYS="$FRONTEND_API_KEY,$TRADEBOT_API_KEY,$ADMIN_API_KEY"

echo -e "${GREEN}✅ API Keys Generated Successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 API Keys (Keep these secure!):${NC}"
echo "=================================================="
echo -e "${GREEN}Frontend API Key:${NC}"
echo "$FRONTEND_API_KEY"
echo ""
echo -e "${GREEN}Trade-bot API Key:${NC}"
echo "$TRADEBOT_API_KEY"
echo ""
echo -e "${GREEN}Admin API Key:${NC}"
echo "$ADMIN_API_KEY"
echo ""
echo -e "${GREEN}Combined Keys (for deployment):${NC}"
echo "$WEBSOCKET_API_KEYS"
echo "=================================================="

# Save to secure file
KEYS_FILE="scripts/websocket_api_keys.env"
cat > "$KEYS_FILE" << EOF
# Oryn WebSocket Service API Keys
# Generated on: $(date)
# Keep this file secure and do not commit to version control!

# Individual API Keys
WEBSOCKET_API_KEY_FRONTEND=$FRONTEND_API_KEY
WEBSOCKET_API_KEY_TRADEBOT=$TRADEBOT_API_KEY
WEBSOCKET_API_KEY_ADMIN=$ADMIN_API_KEY

# Combined keys for deployment
WEBSOCKET_API_KEYS=$WEBSOCKET_API_KEYS

# Allowed origins (update as needed)
ALLOWED_ORIGINS=https://oryntrade.com,https://app.oryntrade.com,http://localhost:3000,http://127.0.0.1:4000

# Export commands for easy use
# export WEBSOCKET_API_KEYS="$WEBSOCKET_API_KEYS"
# export ALLOWED_ORIGINS="https://oryntrade.com,https://app.oryntrade.com,http://localhost:3000,http://127.0.0.1:4000"
EOF

echo -e "${GREEN}💾 Keys saved to: $KEYS_FILE${NC}"
echo ""
echo -e "${YELLOW}🔧 Next Steps:${NC}"
echo "1. Source the keys file: source $KEYS_FILE"
echo "2. Set your Polygon API key: export POLYGON_API_KEY='your_key_here'"
echo "3. Deploy the service: ./scripts/deploy_websocket_production.sh"
echo ""
echo -e "${YELLOW}📱 Client Configuration:${NC}"
echo "Frontend (.env.local):"
echo "REACT_APP_WEBSOCKET_API_KEY=$FRONTEND_API_KEY"
echo ""
echo "Trade-bot (.env):"
echo "WEBSOCKET_API_KEY=$TRADEBOT_API_KEY"
echo ""
echo -e "${RED}⚠️  Security Notes:${NC}"
echo "- Keep these keys secure and private"
echo "- Add $KEYS_FILE to .gitignore"
echo "- Rotate keys regularly (monthly/quarterly)"
echo "- Monitor usage in service logs"
echo ""
echo -e "${GREEN}🎯 Keys generation complete!${NC}"
