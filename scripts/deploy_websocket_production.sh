#!/bin/bash

# Oryn WebSocket Service - Complete Production Deployment Script
# This script handles the full deployment process including configuration updates

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"oryntrade"}
SERVICE_NAME="oryn-websocket-service"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo -e "${BLUE}🚀 Oryn WebSocket Service - Production Deployment${NC}"
echo "=================================================="
echo "This script will:"
echo "1. Deploy WebSocket service to Cloud Run"
echo "2. Update client configurations"
echo "3. Test the deployment"
echo "4. Provide next steps"
echo "=================================================="

# Check prerequisites
echo -e "${YELLOW}🔧 Checking prerequisites...${NC}"

if [ -z "$POLYGON_API_KEY" ]; then
    echo -e "${RED}❌ POLYGON_API_KEY environment variable not set${NC}"
    echo "Please set it with: export POLYGON_API_KEY='your_api_key_here'"
    exit 1
fi

if [ -z "$WEBSOCKET_API_KEYS" ]; then
    echo -e "${YELLOW}⚠️  WEBSOCKET_API_KEYS not set - service will use origin-based authentication only${NC}"
    echo "For better security, generate API keys with: ./scripts/generate_api_keys.sh"
    echo "Then source the keys: source scripts/websocket_api_keys.env"
fi

if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK.${NC}"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker not found. Please install Docker.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Deploy WebSocket service
echo -e "${YELLOW}🚀 Deploying WebSocket service to Cloud Run...${NC}"
cd functions/
./deploy_to_cloud_run.sh

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --platform managed --region ${REGION} --format 'value(status.url)')
WEBSOCKET_URL="${SERVICE_URL/https/wss}/ws"

echo -e "${GREEN}✅ WebSocket service deployed successfully!${NC}"
echo "Service URL: $SERVICE_URL"
echo "WebSocket URL: $WEBSOCKET_URL"

# Update configurations
echo -e "${YELLOW}🔧 Updating client configurations...${NC}"
cd ..

# Update frontend config
if [ -f "frontend/src/config.js" ]; then
    # Create backup
    cp "frontend/src/config.js" "frontend/src/config.js.backup"
    
    # Update the production WebSocket URL
    sed -i.tmp "s|wss://oryn-websocket-service-[^/]*/ws|$WEBSOCKET_URL|g" "frontend/src/config.js"
    rm -f "frontend/src/config.js.tmp"
    
    echo -e "${GREEN}✅ Frontend configuration updated${NC}"
else
    echo -e "${YELLOW}⚠️  Frontend config not found, skipping...${NC}"
fi

# Update trade-bot config example
if [ -f "trade-bot-service/.env.example" ]; then
    # Create backup
    cp "trade-bot-service/.env.example" "trade-bot-service/.env.example.backup"
    
    # Update the production WebSocket URL
    sed -i.tmp "s|wss://oryn-websocket-service-[^/]*/ws|$WEBSOCKET_URL|g" "trade-bot-service/.env.example"
    rm -f "trade-bot-service/.env.example.tmp"
    
    echo -e "${GREEN}✅ Trade-bot configuration example updated${NC}"
else
    echo -e "${YELLOW}⚠️  Trade-bot config example not found, skipping...${NC}"
fi

# Test the deployment
echo -e "${YELLOW}🧪 Testing deployment...${NC}"

# Test health endpoint
if curl -f "${SERVICE_URL}/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed${NC}"
    echo "Please check the logs: gcloud run logs read --service=${SERVICE_NAME} --region=${REGION}"
fi

# Test status endpoint
if curl -f "${SERVICE_URL}/status" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Status endpoint accessible${NC}"
else
    echo -e "${YELLOW}⚠️  Status endpoint not accessible (this might be normal if no data is flowing)${NC}"
fi

# Final summary
echo ""
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo "=================================================="
echo -e "${GREEN}Service URL:${NC} $SERVICE_URL"
echo -e "${GREEN}WebSocket URL:${NC} $WEBSOCKET_URL"
echo -e "${GREEN}Health Check:${NC} ${SERVICE_URL}/health"
echo -e "${GREEN}Status:${NC} ${SERVICE_URL}/status"
echo "=================================================="

echo ""
echo -e "${BLUE}📝 Next Steps:${NC}"
echo "1. Configure your team's environments:"
echo "   - Run: ./scripts/configure_environment.sh production"
echo ""
echo "2. Update your .env files with the new WebSocket URL:"
echo "   - Frontend: Already updated automatically"
echo "   - Trade-bot: Update WEBSOCKET_SERVICE_URL in your .env file"
echo "   - Example: WEBSOCKET_SERVICE_URL=$WEBSOCKET_URL"
echo ""
echo "3. Test the WebSocket connection:"
echo "   - Install wscat: npm install -g wscat"
echo "   - Test: wscat -c $WEBSOCKET_URL"
echo ""
echo "4. Monitor the service:"
echo "   - Logs: gcloud run logs tail --service=${SERVICE_NAME} --region=${REGION}"
echo "   - Metrics: Check Google Cloud Console > Cloud Run > ${SERVICE_NAME}"
echo ""
echo "5. Scale if needed:"
echo "   - gcloud run services update ${SERVICE_NAME} --max-instances=50 --region=${REGION}"
echo ""

echo -e "${GREEN}🎯 Your WebSocket service is now running in production!${NC}"
echo -e "${GREEN}Your team can now connect to the shared WebSocket service.${NC}"
