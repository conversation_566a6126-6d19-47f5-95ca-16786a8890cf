import { useEffect, useRef, useState, useCallback } from 'react';
import { createChart } from 'lightweight-charts';
import axios from 'axios';
import { USE_FIREBASE_EMULATOR } from '../config';

const GET_HISTORICAL_DATA_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/fetch_historical_data_from_polygon"
  : "https://fetch-historical-data-from-polygon-ihjc6tjxia-uc.a.run.app";

// Define trading session hours in UTC with more transparent colors
const TRADING_SESSIONS = {
  "London": {
    start: 7, // 7:00 UTC
    end: 16,  // 16:00 UTC
    color: 'rgba(106, 90, 205, 0.1)' // More transparent purple
  },
  "New York": {
    start: 12, // 12:00 UTC
    end: 21,   // 21:00 UTC
    color: 'rgba(65, 105, 225, 0.1)' // More transparent blue
  },
  "Tokyo": {
    start: 23, // 23:00 UTC
    end: 8,    // 8:00 UTC (next day)
    color: 'rgba(60, 179, 113, 0.1)' // More transparent green
  },
  "Sydney": {
    start: 21, // 21:00 UTC
    end: 6,    // 6:00 UTC (next day)
    color: 'rgba(218, 165, 32, 0.1)' // More transparent golden
  }
};

// Helper function to check if a timestamp is within a trading session
const isInTradingSession = (timestamp, session, userTimezone) => {
  const date = new Date(timestamp * 1000);

  // Convert user's local time to UTC
  const utcHours = date.getUTCHours();
  const sessionHours = TRADING_SESSIONS[session];

  if (!sessionHours) return false;

  if (sessionHours.start < sessionHours.end) {
    // Simple case: session starts and ends on the same day
    return utcHours >= sessionHours.start && utcHours < sessionHours.end;
  } else {
    // Complex case: session spans across midnight
    return utcHours >= sessionHours.start || utcHours < sessionHours.end;
  }
};

const StrategyChart = ({
  forexPair,
  timeframe,
  userTimezone = 'UTC',
  selectedTradingSessions = [],
  data = null,
  isLoading = false,
  error = null,
  indicators = [],
  onIndicatorAdd = null, // Callback for when indicator is being added
  trades = [] // NEW: trades from backtest results
}) => {
  const mainChartRef = useRef(null);
  const indicatorChartsRef = useRef({}); // Store refs for all indicator charts
  const mainChartInstanceRef = useRef(null);
  const indicatorChartInstancesRef = useRef({}); // Store instances for all indicator charts
  const candlestickSeriesRef = useRef(null);
  const volumeSeriesRef = useRef(null);
  const indicatorSeriesRef = useRef({}); // Store series for all indicators
  const resizeObserverRef = useRef(null);
  const resizeObserversRef = useRef({}); // Store individual resize observers for indicator charts
  const sessionSeriesRefs = useRef({});
  const toggleButtonRef = useRef(null);
  const [loadingMessage, setLoadingMessage] = useState('Loading chart data...');
  const [showVolume, setShowVolume] = useState(false);
  const [indicatorDisplayPrefs, setIndicatorDisplayPrefs] = useState({}); // Store display preferences for indicators
  const [showPreferenceDialog, setShowPreferenceDialog] = useState(false);
  const [pendingIndicator, setPendingIndicator] = useState(null);
  const [indicatorGroups, setIndicatorGroups] = useState({});
  const [mainChartIndicators, setMainChartIndicators] = useState({});
  const mainChartSeriesRef = useRef({});

  // Debug log for props
  useEffect(() => {
    console.log('StrategyChart props changed:', {
      selectedTradingSessions,
      userTimezone,
      dataPoints: data?.length,
      hasChart: !!mainChartRef.current
    });
  }, [selectedTradingSessions, userTimezone, data]);

  // Function to create trading session highlights
  const createTradingSessionHighlights = useCallback((chart, candleData) => {
    if (!chart || !candleData || !selectedTradingSessions.length) {
      console.log('Skipping session highlights:', {
        hasChart: !!chart,
        hasData: !!candleData,
        sessions: selectedTradingSessions
      });
      return;
    }

    console.log('Creating trading session highlights:', {
      sessions: selectedTradingSessions,
      candleCount: candleData.length
    });

    // Clear existing session series
    Object.values(sessionSeriesRefs.current).forEach(series => {
      if (series && chart) {
        try {
          chart.removeSeries(series);
        } catch (error) {
          console.error('Error removing series:', error);
        }
      }
    });
    sessionSeriesRefs.current = {};

    // Find the overall price range
    const minPrice = Math.min(...candleData.map(d => d.low));
    const maxPrice = Math.max(...candleData.map(d => d.high));
    const priceRange = maxPrice - minPrice;
    const extendedMin = minPrice - priceRange * 0.1;
    const extendedMax = maxPrice + priceRange * 0.1;

    selectedTradingSessions.forEach(session => {
      const sessionConfig = TRADING_SESSIONS[session];
      if (!sessionConfig) {
        console.log('Session not found:', session);
        return;
      }

      console.log('Processing session:', session, sessionConfig);

      try {
        // Create a background series for the session
        const sessionSeries = chart.addAreaSeries({
          lastValueVisible: false,
          crosshairMarkerVisible: false,
          lineColor: 'transparent',
          topColor: sessionConfig.color,
          bottomColor: sessionConfig.color,
          priceLineVisible: false
        });

        // Generate session background data
        const sessionData = [];
        candleData.forEach((candle) => {
          const date = new Date(candle.time * 1000);
          const hour = date.getUTCHours();
          const { start, end } = sessionConfig;

          const isInSession = start < end
            ? (hour >= start && hour < end)
            : (hour >= start || hour < end);

          if (isInSession) {
            sessionData.push({
              time: candle.time,
              value: extendedMax
            });
          } else {
            sessionData.push({
              time: candle.time,
              value: extendedMin
            });
          }
        });

        // Set the data
        sessionSeries.setData(sessionData);
        sessionSeriesRefs.current[session] = sessionSeries;

        console.log(`Created session highlight for ${session} with ${sessionData.length} points`);
      } catch (error) {
        console.error(`Error creating session highlight for ${session}:`, error);
      }
    });
  }, [selectedTradingSessions]);

  // Helper function to check if a timestamp is within a trading session
  const isInTradingSession = useCallback((timestamp, session) => {
    const date = new Date(timestamp * 1000);
    const utcHours = date.getUTCHours();
    const sessionHours = TRADING_SESSIONS[session];

    if (!sessionHours) return false;

    if (sessionHours.start < sessionHours.end) {
      // Simple case: session starts and ends on the same day
      return utcHours >= sessionHours.start && utcHours < sessionHours.end;
    } else {
      // Complex case: session spans across midnight
      return utcHours >= sessionHours.start || utcHours < sessionHours.end;
    }
  }, []);

  // Function to create main chart
  const createMainChartInstance = () => {
    if (!mainChartRef.current) return null;

    // Clear previous chart if it exists
    if (mainChartInstanceRef.current) {
      mainChartInstanceRef.current.remove();
      mainChartInstanceRef.current = null;
      candlestickSeriesRef.current = null;
      volumeSeriesRef.current = null;
      sessionSeriesRefs.current = {};
    }

    // Create new chart
    const chart = createChart(mainChartRef.current, {
      width: mainChartRef.current.clientWidth,
      height: 400,
      layout: {
        background: { color: '#0A0B0B' },
        textColor: '#FEFEFF',
        fontSize: 12,
        fontFamily: 'Inter, sans-serif',
      },
      localization: {
        priceFormatter: price => price.toFixed(5),
      },
      grid: {
        vertLines: { color: '#1E222D' },
        horzLines: { color: '#1E222D' },
      },
      crosshair: {
        mode: 1,
        vertLine: {
          width: 1,
          color: '#EFBD3A',
          style: 0,
          labelBackgroundColor: '#EFBD3A',
        },
        horzLine: {
          width: 1,
          color: '#EFBD3A',
          style: 0,
          labelBackgroundColor: '#EFBD3A',
        },
      },
      timeScale: {
        borderColor: '#1E222D',
        timeVisible: true,
        secondsVisible: false,
        fixRightEdge: false, // Allow scrolling past the last candle
        fixLeftEdge: false, // Allow scrolling past the first candle
        rightBarStaysOnScroll: false, // Prevent hovered bar from moving when scrolling
        lockVisibleTimeRangeOnResize: false, // Allow time range to change on resize
        rightOffset: 12, // Add some space on the right for scrolling
        tickMarkFormatter: (time) => {
          // Convert timestamp to Date object and ensure we're using UTC
          const date = new Date(time * 1000);

          // Format the date in UTC time
          const month = date.getUTCMonth() + 1;
          const day = date.getUTCDate();
          const hours = date.getUTCHours().toString().padStart(2, '0');
          const minutes = date.getUTCMinutes().toString().padStart(2, '0');

          // Return formatted date string
          return `${month}/${day} ${hours}:${minutes} UTC`;
        },
      },
      rightPriceScale: {
        borderColor: '#1E222D',
        scaleMargins: {
          top: 0.1,
          bottom: 0.2,
        },
        entireTextOnly: false, // Allow partial text to ensure all price levels are visible
        mode: 0, // Normal mode (not logarithmic)
        format: {
          type: 'price',
          precision: 5,
          minMove: 0.00001,
        },
        autoScale: true, // Ensure the chart auto-scales to show all price levels
      }
    });

    // Add candlestick series with proper price format for forex
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderUpColor: '#26a69a',
      borderDownColor: '#ef5350',
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      priceFormat: {
        type: 'price',
        precision: 5,
        minMove: 0.00001,
      },
      // Ensure the price scale is properly configured
      priceScaleId: 'right',
    });

    // Configure the price scale to ensure all price levels are visible
    chart.priceScale('right').applyOptions({
      autoScale: true,
      mode: 0, // Normal mode (not logarithmic)
      entireTextOnly: false, // Allow partial text to ensure all price levels are visible
      borderVisible: true,
      scaleMargins: {
        top: 0.1,
        bottom: 0.2,
      },
    });

    // Add volume series
    const volumeSeries = chart.addHistogramSeries({
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: '',
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
      visible: showVolume
    });

    // Store references
    mainChartInstanceRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;
    volumeSeriesRef.current = volumeSeries;

    return { chart, candlestickSeries, volumeSeries };
  };

  // Function to create indicator chart
  const createIndicatorChart = useCallback((containerId, height = 200, type = 'RSI') => {
    const originalContainer = indicatorChartsRef.current[containerId];
    if (!originalContainer) return null;

    // If chart already exists, just return it
    if (indicatorChartInstancesRef.current[containerId]) {
      return indicatorChartInstancesRef.current[containerId];
    }

    // Create container for chart and title
    const container = document.createElement('div');
    container.style.position = 'relative';
    container.style.width = '100%';
    container.style.minWidth = '300px';
    container.style.maxWidth = '100%';
    container.style.height = `${height + 30}px`; // Add space for title
    container.style.overflow = 'hidden';
    container.style.resize = 'horizontal';

    // Create title element
    const title = document.createElement('div');
    title.style.position = 'absolute';
    title.style.top = '0';
    title.style.left = '0';
    title.style.width = '100%';
    title.style.height = '30px';
    title.style.display = 'flex';
    title.style.alignItems = 'center';
    title.style.paddingLeft = '10px';
    title.style.fontSize = '14px';
    title.style.fontWeight = '500';
    title.style.color = '#FEFEFF';
    title.style.backgroundColor = '#0A0B0B';
    if (type === 'RSI') {
      title.textContent = 'Relative Strength Index (RSI)';
    } else if (type === 'MACD') {
      title.textContent = 'Moving Average Convergence Divergence (MACD)';
    } else if (type === 'ATR') {
      title.textContent = 'Average True Range (ATR)';
    } else {
      title.textContent = type;
    }
    container.appendChild(title);

    // Create chart container
    const chartContainer = document.createElement('div');
    chartContainer.style.position = 'absolute';
    chartContainer.style.top = '30px';
    chartContainer.style.left = '0';
    chartContainer.style.width = '100%';
    chartContainer.style.minWidth = '300px';
    chartContainer.style.maxWidth = '100%';
    chartContainer.style.height = `${height}px`;
    chartContainer.style.overflow = 'hidden';
    container.appendChild(chartContainer);

    // Replace the original container with our new container
    if (originalContainer.parentNode) {
      originalContainer.parentNode.replaceChild(container, originalContainer);
      indicatorChartsRef.current[containerId] = chartContainer;

      // Add a MutationObserver to detect size changes
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          if (indicatorChartInstancesRef.current[containerId]) {
            indicatorChartInstancesRef.current[containerId].applyOptions({
              width: entry.contentRect.width,
            });
            console.log(`Indicator chart ${containerId} container resized to:`, entry.contentRect.width);
          }
        }
      });

      resizeObserver.observe(chartContainer);
      resizeObserver.observe(container);

      // Store the observer for cleanup
      if (!resizeObserversRef.current) resizeObserversRef.current = {};
      resizeObserversRef.current[containerId] = resizeObserver;
    } else {
      console.warn('Original container has no parent node');
      return null;
    }

    const chart = createChart(chartContainer, {
      width: chartContainer.clientWidth,
      height: height,
      layout: {
        background: { color: '#0A0B0B' },
        textColor: '#FEFEFF',
        fontSize: 12,
        fontFamily: 'Inter, sans-serif',
      },
      localization: {
        priceFormatter: price => price.toFixed(5),
      },
      grid: {
        vertLines: { color: '#1E222D' },
        horzLines: { color: '#1E222D' },
      },
      crosshair: {
        mode: 1,
        vertLine: {
          width: 1,
          color: '#EFBD3A',
          style: 0,
          labelBackgroundColor: '#EFBD3A',
        },
        horzLine: {
          width: 1,
          color: '#EFBD3A',
          style: 0,
          labelBackgroundColor: '#EFBD3A',
        },
      },
      timeScale: {
        borderColor: '#1E222D',
        timeVisible: true,
        secondsVisible: false,
        fixRightEdge: false, // Allow scrolling past the last candle
        fixLeftEdge: false, // Allow scrolling past the first candle
        rightBarStaysOnScroll: false, // Prevent hovered bar from moving when scrolling
        lockVisibleTimeRangeOnResize: false, // Allow time range to change on resize
        rightOffset: 12, // Add some space on the right for scrolling
        tickMarkFormatter: (time) => {
          // Convert timestamp to Date object and ensure we're using UTC
          const date = new Date(time * 1000);

          // Format the date in UTC time
          const month = date.getUTCMonth() + 1;
          const day = date.getUTCDate();
          const hours = date.getUTCHours().toString().padStart(2, '0');
          const minutes = date.getUTCMinutes().toString().padStart(2, '0');

          // Return formatted date string
          return `${month}/${day} ${hours}:${minutes} UTC`;
        },
      },
      rightPriceScale: {
        borderColor: '#1E222D',
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
        entireTextOnly: true,
        mode: 0, // Normal mode (not logarithmic)
        format: {
          type: 'price',
          precision: 5,
          minMove: 0.00001,
        }
      }
    });

    indicatorChartInstancesRef.current[containerId] = chart;
    return chart;
  }, []);

  // Function to create and update volume toggle button
  const updateVolumeToggle = useCallback(() => {
    // Remove existing button if it exists
    if (toggleButtonRef.current) {
      toggleButtonRef.current.remove();
    }

    // Create new button container
    const toggleContainer = document.createElement('div');
    toggleContainer.style.position = 'absolute';
    toggleContainer.style.top = '5px';
    toggleContainer.style.right = '5px';
    toggleContainer.style.zIndex = '2';

    const toggleButton = document.createElement('button');
    toggleButton.className = 'px-2 py-1 text-xs rounded transition-all duration-200';
    toggleButton.style.backgroundColor = showVolume ? 'rgba(38, 166, 154, 0.2)' : 'rgba(239, 83, 80, 0.2)';
    toggleButton.style.border = `1px solid ${showVolume ? '#26a69a' : '#ef5350'}`;
    toggleButton.style.color = '#FEFEFF';
    toggleButton.style.cursor = 'pointer';
    toggleButton.textContent = `Volume ${showVolume ? 'On' : 'Off'}`;

    toggleButton.onclick = () => {
      const newShowVolume = !showVolume;
      setShowVolume(newShowVolume);
      if (volumeSeriesRef.current) {
        volumeSeriesRef.current.applyOptions({ visible: newShowVolume });
      }
    };

    toggleContainer.appendChild(toggleButton);
    if (mainChartRef.current) {
      mainChartRef.current.appendChild(toggleContainer);
      toggleButtonRef.current = toggleContainer;
    }
  }, [showVolume]);

  // Effect to update volume toggle when showVolume changes
  useEffect(() => {
    if (mainChartRef.current) {
      updateVolumeToggle();
    }
  }, [showVolume, updateVolumeToggle]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Clean up the main resize observer
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }

      // Clean up individual resize observers for indicator charts
      if (resizeObserversRef.current) {
        Object.values(resizeObserversRef.current).forEach(observer => {
          if (observer) {
            observer.disconnect();
          }
        });
        resizeObserversRef.current = {};
      }

      // Clean up main chart and its series
      if (mainChartInstanceRef.current) {
        // Clean up main chart series
        Object.values(mainChartSeriesRef.current).forEach(({ series }) => {
          if (series) {
            try {
              mainChartInstanceRef.current.removeSeries(series);
            } catch (error) {
              console.warn('Error removing main chart series:', error);
            }
          }
        });
        mainChartSeriesRef.current = {};

        // Remove the main chart instance
        mainChartInstanceRef.current.remove();
        mainChartInstanceRef.current = null;
      }

      // Clean up indicator charts and their series
      Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
        if (chart) {
          // Remove all series from this chart
          if (indicatorSeriesRef.current[id]) {
            try {
              chart.removeSeries(indicatorSeriesRef.current[id]);
            } catch (error) {
              console.warn('Error removing indicator series:', error);
            }
            delete indicatorSeriesRef.current[id];
          }
          // Remove the chart instance
          chart.remove();
          delete indicatorChartInstancesRef.current[id];
        }
      });

      // Clear all refs
      candlestickSeriesRef.current = null;
      volumeSeriesRef.current = null;
      sessionSeriesRefs.current = {};

      // Remove volume toggle button
      if (toggleButtonRef.current) {
        toggleButtonRef.current.remove();
      }
    };
  }, []);

  // Function to calculate RSI
  const calculateRSI = useCallback((data, period = 14, source = 'close', existingIndicators = {}) => {
    // Get source data points
    const getSourceData = () => {
      // If source is an indicator ID, use that indicator's data
      if (existingIndicators[source]) {
        return data.map((candle, i) => {
          const indicatorData = existingIndicators[source];
          return indicatorData[i]?.value || 0;
        });
      }

      // Otherwise, use price data
      switch (source.toLowerCase()) {
        case 'open':
          return data.map(d => d.open);
        case 'high':
          return data.map(d => d.high);
        case 'low':
          return data.map(d => d.low);
        case 'volume':
          return data.map(d => d.volume);
        case 'close':
        default:
          return data.map(d => d.close);
      }
    };

    const sourceData = getSourceData();
    const rsiData = [];
    let gains = [];
    let losses = [];

    // Calculate price changes
    for (let i = 1; i < sourceData.length; i++) {
      const change = sourceData[i] - sourceData[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? -change : 0);
    }

    // Calculate initial average gain and loss
    const avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;

    let prevAvgGain = avgGain;
    let prevAvgLoss = avgLoss;

    // Add first RSI value
    rsiData.push({
      time: data[period].time,
      value: 100 - (100 / (1 + avgGain / (avgLoss || 1)))
    });

    // Calculate subsequent RSI values
    for (let i = period + 1; i < sourceData.length; i++) {
      const gain = gains[i - 1];
      const loss = losses[i - 1];

      const newAvgGain = (prevAvgGain * (period - 1) + gain) / period;
      const newAvgLoss = (prevAvgLoss * (period - 1) + loss) / period;

      prevAvgGain = newAvgGain;
      prevAvgLoss = newAvgLoss;

      rsiData.push({
        time: data[i].time,
        value: 100 - (100 / (1 + newAvgGain / (newAvgLoss || 1)))
      });
    }

    return rsiData;
  }, []);

  // Function to calculate SMA
  const calculateSMA = useCallback((data, period, source = 'close', existingIndicators = {}) => {
    // Get source data points
    const getSourceData = () => {
      // If source is an indicator ID, use that indicator's data
      if (existingIndicators[source]) {
        return existingIndicators[source].map(point => point.value);
      }

      // Otherwise, use price data
      switch (source.toLowerCase()) {
        case 'open':
          return data.map(d => d.open);
        case 'high':
          return data.map(d => d.high);
        case 'low':
          return data.map(d => d.low);
        case 'volume':
          return data.map(d => d.volume);
        case 'close':
        default:
          return data.map(d => d.close);
      }
    };

    const sourceData = getSourceData();
    const smaData = [];

    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        continue;
      }
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += sourceData[i - j];
      }
      smaData.push({
        time: data[i].time,
        value: sum / period
      });
    }
    return smaData;
  }, []);

  // Add EMA calculation function after the calculateSMA function
  const calculateEMA = useCallback((data, period, source = 'close', existingIndicators = {}) => {
    // Get source data points
    const getSourceData = () => {
      // If source is an indicator ID, use that indicator's data
      if (existingIndicators[source]) {
        return existingIndicators[source].map(point => point.value);
      }

      // Otherwise, use price data
      switch (source.toLowerCase()) {
        case 'open':
          return data.map(d => d.open);
        case 'high':
          return data.map(d => d.high);
        case 'low':
          return data.map(d => d.low);
        case 'volume':
          return data.map(d => d.volume);
        case 'close':
        default:
          return data.map(d => d.close);
      }
    };

    const sourceData = getSourceData();
    const emaData = [];
    const multiplier = 2 / (period + 1);

    // Calculate first SMA as initial EMA
    let sum = 0;
    for (let i = 0; i < period; i++) {
      sum += sourceData[i];
    }
    let prevEma = sum / period;

    // Add first EMA point
    emaData.push({
      time: data[period - 1].time,
      value: prevEma
    });

    // Calculate subsequent EMA values
    for (let i = period; i < data.length; i++) {
      const currentPrice = sourceData[i];
      const currentEma = (currentPrice - prevEma) * multiplier + prevEma;
      prevEma = currentEma;

      emaData.push({
        time: data[i].time,
        value: currentEma
      });
    }

    return emaData;
  }, []);

  // Add MACD calculation function after the calculateSMA function
  const calculateMACD = useCallback((data, { fast = 12, slow = 26, signal = 9 }) => {
    // Calculate EMAs
    const calculateEMA = (values, period) => {
      const k = 2 / (period + 1);
      let ema = values[0];
      const results = [ema];

      for (let i = 1; i < values.length; i++) {
        ema = values[i] * k + ema * (1 - k);
        results.push(ema);
      }
      return results;
    };

    // Get closing prices
    const closes = data.map(d => d.close);

    // Calculate fast and slow EMAs
    const fastEMA = calculateEMA(closes, fast);
    const slowEMA = calculateEMA(closes, slow);

    // Calculate MACD line
    const macdLine = fastEMA.map((fast, i) => fast - slowEMA[i]);

    // Calculate signal line
    const signalLine = calculateEMA(macdLine, signal);

    // Calculate histogram
    const histogram = macdLine.map((macd, i) => macd - signalLine[i]);

    // Format data for chart
    return data.map((d, i) => ({
      time: d.time,
      macd: macdLine[i],
      signal: signalLine[i],
      histogram: histogram[i]
    }));
  }, []);

  // Function to calculate Bollinger Bands
  const calculateBollingerBands = useCallback((data, period = 20, stdDev = 2, offset = 0, source = 'close', existingIndicators = {}) => {
    // Get source data points
    const getSourceData = () => {
      // If source is an indicator ID, use that indicator's data
      if (existingIndicators[source]) {
        return existingIndicators[source].map(point => point.value);
      }

      // Otherwise, use price data
      switch (source.toLowerCase()) {
        case 'open':
          return data.map(d => d.open);
        case 'high':
          return data.map(d => d.high);
        case 'low':
          return data.map(d => d.low);
        case 'volume':
          return data.map(d => d.volume);
        case 'close':
        default:
          return data.map(d => d.close);
      }
    };

    const sourceData = getSourceData();
    const bollingerData = [];

    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        continue;
      }

      // Calculate SMA
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += sourceData[i - j];
      }
      const sma = sum / period;

      // Calculate standard deviation
      let sumSquaredDiff = 0;
      for (let j = 0; j < period; j++) {
        const diff = sourceData[i - j] - sma;
        sumSquaredDiff += diff * diff;
      }
      const stdDeviation = Math.sqrt(sumSquaredDiff / period);

      // Calculate upper and lower bands with offset
      const upperBand = sma + (stdDeviation * stdDev) + offset;
      const lowerBand = sma - (stdDeviation * stdDev) + offset;

      bollingerData.push({
        time: data[i].time,
        middle: sma + offset, // Apply offset to middle band as well
        upper: upperBand,
        lower: lowerBand
      });
    }

    return bollingerData;
  }, []);

  // Function to calculate ATR
  const calculateATR = useCallback((data, period = 14) => {
    const atrData = [];
    const trueRanges = [];

    // Calculate True Range for each period
    for (let i = 1; i < data.length; i++) {
      const high = data[i].high;
      const low = data[i].low;
      const prevClose = data[i - 1].close;

      const tr1 = high - low;
      const tr2 = Math.abs(high - prevClose);
      const tr3 = Math.abs(low - prevClose);

      const trueRange = Math.max(tr1, tr2, tr3);
      trueRanges.push(trueRange);
    }

    // Calculate initial ATR
    let atr = trueRanges.slice(0, period).reduce((a, b) => a + b, 0) / period;

    // Add first ATR value at data[period].time
    if (data.length > period) {
      atrData.push({
        time: data[period].time,
        value: atr
      });
    }

    // Calculate subsequent ATR values
    for (let i = period + 1; i < data.length; i++) {
      const currentTR = trueRanges[i - 1];
      atr = ((atr * (period - 1)) + currentTR) / period;

      atrData.push({
        time: data[i].time,
        value: atr
      });
    }

    return atrData;
  }, []);

  // Function to handle indicator display preference
  const handleIndicatorPreference = useCallback((indicator, preference) => {
    setIndicatorDisplayPrefs(prev => {
      const newPrefs = {
        ...prev,
        [indicator.id]: {
          type: indicator.type,
          window: preference,
          groupId: preference === 'existing' ?
            Object.values(prev).find(p => p.type === indicator.type)?.groupId || indicator.id :
            indicator.id
        }
      };
      return newPrefs;
    });
    setShowPreferenceDialog(false);
    setPendingIndicator(null);

    // Call the onIndicatorAdd callback with success=true
    if (onIndicatorAdd) {
      console.log('Calling onIndicatorAdd with success=true from handleIndicatorPreference');
      onIndicatorAdd(true);
    }
  }, [onIndicatorAdd]);

  // Function to check if there are existing windows of a specific type
  const hasExistingWindows = useCallback((type) => {
    return Object.values(indicatorGroups).some(group =>
      group.indicators &&
      group.indicators.length > 0 &&
      group.indicators[0].type === type
    );
  }, [indicatorGroups]);

  // Function to calculate Support & Resistance levels
  const calculateSupportResistance = useCallback((data, leftBars = 10, rightBars = 10) => {
    if (!data || data.length < leftBars + rightBars + 1) {
      return [];
    }

    // Find the price range of the data
    const minPrice = Math.min(...data.map(d => d.low));
    const maxPrice = Math.max(...data.map(d => d.high));
    console.log(`Price range in data: ${minPrice.toFixed(5)} - ${maxPrice.toFixed(5)}`);

    // Validate the data to ensure we have valid price values
    if (isNaN(minPrice) || isNaN(maxPrice) || minPrice === maxPrice) {
      console.error('Invalid price data for Support & Resistance calculation');
      return [];
    }

    // Improved approach to find swing highs and lows
    const levels = [];

    // Focus more on recent price action - give higher weight to recent swings
    // We'll use a weighted approach where more recent swings get higher priority
    const recentDataThreshold = Math.floor(data.length * 0.7); // Consider the most recent 70% of data as "recent"

    // Use a simpler approach to find ALL swing points
    // A swing high is defined as a candle where the high is higher than all candles within the lookback period
    // A swing low is defined as a candle where the low is lower than all candles within the lookback period

    // Find pivot points (local highs and lows)
    for (let i = leftBars; i < data.length - rightBars; i++) {
      const currentCandle = data[i];

      // Check if this is a swing high (resistance)
      let isSwingHigh = true;

      // Check left side
      for (let j = i - leftBars; j < i; j++) {
        if (data[j].high >= currentCandle.high) {
          isSwingHigh = false;
          break;
        }
      }

      // If still a potential swing high, check right side
      if (isSwingHigh) {
        for (let j = i + 1; j <= i + rightBars; j++) {
          if (data[j].high >= currentCandle.high) {
            isSwingHigh = false;
            break;
          }
        }
      }

      // Check if this is a swing low (support)
      let isSwingLow = true;

      // Check left side
      for (let j = i - leftBars; j < i; j++) {
        if (data[j].low <= currentCandle.low) {
          isSwingLow = false;
          break;
        }
      }

      // If still a potential swing low, check right side
      if (isSwingLow) {
        for (let j = i + 1; j <= i + rightBars; j++) {
          if (data[j].low <= currentCandle.low) {
            isSwingLow = false;
            break;
          }
        }
      }

      // Calculate a recency factor - more recent swings get higher weight
      const recencyFactor = i >= recentDataThreshold ? 2 : 1;

      // Add to levels if it's a pivot point
      if (isSwingHigh) {
        // Always add the swing high as a resistance level
        levels.push({
          value: currentCandle.high,
          type: 'resistance',
          time: currentCandle.time,
          candleIndex: i,
          strength: recencyFactor, // Recent swings start with higher strength
          isRecent: i >= recentDataThreshold
        });
      }

      if (isSwingLow) {
        // Always add the swing low as a support level
        levels.push({
          value: currentCandle.low,
          type: 'support',
          time: currentCandle.time,
          candleIndex: i,
          strength: recencyFactor, // Recent swings start with higher strength
          isRecent: i >= recentDataThreshold
        });
      }
    }

    console.log(`Found ${levels.length} initial pivot points`);

    // Group very similar levels that are extremely close to each other
    // This helps avoid having multiple lines at virtually the same price
    const groupedLevels = [];
    const tolerance = 0.0005; // Very small tolerance (0.05%) to only group extremely similar levels

    // Group similar levels
    levels.forEach(level => {
      const similarLevelIndex = groupedLevels.findIndex(existingLevel =>
        existingLevel.type === level.type &&
        Math.abs(existingLevel.value - level.value) / level.value < tolerance
      );

      if (similarLevelIndex >= 0) {
        // If we found a very similar level, keep the more recent one
        if (level.candleIndex > groupedLevels[similarLevelIndex].candleIndex) {
          groupedLevels[similarLevelIndex] = {...level};
        }
      } else {
        // If no similar level found, add this one
        groupedLevels.push({...level});
      }
    });

    console.log(`Grouped into ${groupedLevels.length} levels`);

    // Get the current price (last candle close)
    const currentPrice = data[data.length - 1].close;

    // Sort levels by recency (most recent first)
    const sortedLevels = [...groupedLevels].sort((a, b) => b.candleIndex - a.candleIndex);

    // Don't limit the number of levels - show all swing points
    // But separate into support and resistance for easier handling
    const supportLevels = sortedLevels.filter(level => level.type === 'support');
    const resistanceLevels = sortedLevels.filter(level => level.type === 'resistance');

    console.log(`Final levels: ${supportLevels.length} support, ${resistanceLevels.length} resistance`);

    // Return combined levels
    return [...supportLevels, ...resistanceLevels];
  }, []);

  // Function to group indicators
  const groupIndicators = useCallback(() => {
    console.log('groupIndicators: input indicators:', indicators);
    const groups = {};
    const mainGroups = {};

    indicators.forEach(indicator => {
      console.log('Processing indicator:', indicator);
      if (!indicator || !indicator.type) {
        console.warn('Invalid indicator found:', indicator);
        return;
      }

      let prefInfo = indicatorDisplayPrefs[indicator.id];

      if (
        indicator.type === 'RSI' ||
        indicator.type === 'MACD' ||
        indicator.type === 'ATR' // <-- Treat ATR as a subwindow indicator
      ) {
        if (!prefInfo) {
          // Only show the dialog if there's already an indicator of the same type
          if (hasExistingWindows(indicator.type)) {
            setPendingIndicator(indicator);
            setShowPreferenceDialog(true);
            return;
          } else {
            // If there's no existing window of this type, automatically create a new window
            // without showing the dialog
            const newPref = {
              type: indicator.type,
              window: 'new',
              groupId: indicator.id
            };
            setIndicatorDisplayPrefs(prev => ({
              ...prev,
              [indicator.id]: newPref
            }));

            // Call the onIndicatorAdd callback with success=true
            if (onIndicatorAdd) {
              console.log('Automatically creating new window for first indicator of type:', indicator.type);
              onIndicatorAdd(true);
            }

            prefInfo = newPref; // Use the newly created preference
          }
        }

        const groupId = prefInfo.groupId;
        if (!groups[groupId]) {
          groups[groupId] = {
            indicators: [],
            smaIndicators: [],
            bollingerIndicators: []
          };
        }
        groups[groupId].indicators.push(indicator);
      } else if (
        indicator.type === 'SMA' ||
        indicator.type === 'EMA' ||
        indicator.type === 'BollingerBands' ||
        indicator.type === 'SupportResistance' // Add Support & Resistance to main chart
      ) {
        // Check if indicator is based on another indicator
        const sourceIndicator = indicators.find(ind => ind.id === indicator.source);

        if (sourceIndicator) {
          // If source is another indicator, add to that indicator's group
          const sourceGroupId = indicatorDisplayPrefs[sourceIndicator.id]?.groupId;
          if (sourceGroupId) {
            if (!groups[sourceGroupId]) {
              groups[sourceGroupId] = {
                indicators: [],
                smaIndicators: [],
                bollingerIndicators: []
              };
            }
            if (indicator.type === 'SMA') {
              groups[sourceGroupId].smaIndicators.push(indicator);
            } else if (indicator.type === 'EMA') {
              groups[sourceGroupId].smaIndicators.push(indicator); // EMAs can be added to the same group as SMAs
            } else if (indicator.type === 'BollingerBands') {
              groups[sourceGroupId].bollingerIndicators.push(indicator);
            }
          } else {
            console.warn('Source indicator group not found for indicator:', indicator);
          }
        } else {
          // If source is price data, add to main chart
          if (!mainGroups[indicator.type]) {
            mainGroups[indicator.type] = [];
          }
          mainGroups[indicator.type].push(indicator);
        }
      }
    });

    console.log('mainGroups after grouping:', mainGroups);
    setIndicatorGroups(groups);
    setMainChartIndicators(mainGroups);
  }, [indicators, indicatorDisplayPrefs]);

  // Effect to update groups when indicators or preferences change
  useEffect(() => {
    groupIndicators();
  }, [indicators, indicatorDisplayPrefs, groupIndicators]);

  // Function to get source label for indicators
  const getSourceLabel = useCallback((source, indicators) => {
    if (!source) return 'Close';

    // Check if source is another indicator
    const sourceIndicator = indicators.find(ind => ind.id === source);
    if (sourceIndicator) {
      return `${sourceIndicator.type}(${sourceIndicator.parameters.period})`;
    }

    // Otherwise, it's a price source
    return source.charAt(0).toUpperCase() + source.slice(1).toLowerCase();
  }, []);

  // Function to update charts
  const updateCharts = useCallback(() => {
    console.log('🔄 Starting chart update with:', {
      hasData: !!data,
      dataPoints: data?.length,
      hasContainer: !!mainChartRef.current,
      tradesCount: trades?.length
    });

    if (!data || !mainChartRef.current) {
      console.log('❌ Skipping chart update - missing data or container');
      return;
    }

    // Create a map of existing indicator data
    const existingIndicatorData = {};

    // First, calculate and store data for main chart indicators (SMA, EMA, Bollinger Bands, ATR)
    if (mainChartIndicators.SMA && Array.isArray(mainChartIndicators.SMA)) {
      try {
        mainChartIndicators.SMA.forEach((indicator) => {
          if (!indicator || !indicator.parameters || typeof indicator.parameters.period !== 'number') {
            return;
          }
          const smaData = calculateSMA(data, indicator.parameters.period, indicator.source || 'close', existingIndicatorData);
          if (smaData && Array.isArray(smaData)) {
            existingIndicatorData[indicator.id] = smaData;
          }
        });
      } catch (error) {
        console.error('Error calculating SMA data:', error);
      }
    }

    // Calculate EMA data
    if (mainChartIndicators.EMA && Array.isArray(mainChartIndicators.EMA)) {
      try {
        mainChartIndicators.EMA.forEach((indicator) => {
          if (!indicator || !indicator.parameters || typeof indicator.parameters.period !== 'number') {
            return;
          }
          const emaData = calculateEMA(data, indicator.parameters.period, indicator.source || 'close', existingIndicatorData);
          if (emaData && Array.isArray(emaData)) {
            existingIndicatorData[indicator.id] = emaData;
          }
        });
      } catch (error) {
        console.error('Error calculating EMA data:', error);
      }
    }

    // Update main chart
    console.log('📊 Creating main chart instance');
    const { chart, candlestickSeries, volumeSeries } = createMainChartInstance();

    if (!chart) {
      console.error('❌ Failed to create main chart instance');
      return;
    }

    // Create trading session highlights
    createTradingSessionHighlights(chart, data);

    // Set candlestick data
    console.log(`📈 Setting candlestick data (${data.length} points)`);
    candlestickSeries.setData(data);

    // --- TRADE MARKERS ---
    console.log('🎯 Processing trade markers:', {
      tradesReceived: trades?.length,
      isArray: Array.isArray(trades),
      sampleTrade: trades?.[0]
    });

    if (Array.isArray(trades) && trades.length > 0) {
      const markers = [];
      trades.forEach((trade, idx) => {
        console.log(`📍 Processing trade ${idx + 1}:`, {
          entry_time: trade.entry_time,
          exit_time: trade.exit_time,
          type: trade.type,
          pnl: trade.net_pnl
        });

        // Parse times (assume trade.entry_time and trade.exit_time are ISO strings)
        let entryTime = null, exitTime = null;
        try {
          if (trade.entry_time) entryTime = Math.floor(new Date(trade.entry_time).getTime() / 1000);
          if (trade.exit_time) exitTime = Math.floor(new Date(trade.exit_time).getTime() / 1000);
        } catch (error) {
          console.error('❌ Error parsing trade times:', error);
        }

        // Determine colors and styles based on trade type and outcome
        const isLong = trade.type === 'long';
        const isWin = trade.net_pnl >= 0;
        const entryColor = isLong ? '#26a69a' : '#ef5350';  // Green for longs, Red for shorts
        const exitColor = isWin ? '#26a69a' : '#ef5350';    // Green for wins, Red for losses

        // Entry marker
        if (entryTime && trade.entry_price) {
          const entryMarker = {
            time: entryTime,
            position: 'belowBar',
            color: entryColor,
            shape: isLong ? 'arrowUp' : 'arrowDown',
            text: `${isLong ? '🔼 LONG' : '🔽 SHORT'} Entry\n${trade.entry_price.toFixed(5)}`,
            id: `entry-${idx}`,
            size: 2,
            customData: {
              type: trade.type,
              entry_price: trade.entry_price,
              entry_rule: trade.entry_rule,
              net_pnl: trade.net_pnl,
              exit_price: trade.exit_price,
              exit_rule: trade.exit_rule,
              exit_reason: trade.exit_reason
            }
          };
          markers.push(entryMarker);
        }

        // Exit marker
        if (exitTime && trade.exit_price) {
          const pnlText = trade.net_pnl >= 0 ? `+${trade.net_pnl.toFixed(2)}` : trade.net_pnl.toFixed(2);
          const exitMarker = {
            time: exitTime,
            position: 'aboveBar',
            color: exitColor,
            shape: 'circle',
            text: `${isLong ? '🔼' : '🔽'} Exit ${trade.exit_price.toFixed(5)}\nP/L: ${pnlText}${trade.exit_reason ? `\n${trade.exit_reason}` : ''}`,
            id: `exit-${idx}`,
            size: 2,
            customData: {
              type: trade.type,
              entry_price: trade.entry_price,
              entry_rule: trade.entry_rule,
              net_pnl: trade.net_pnl,
              exit_price: trade.exit_price,
              exit_rule: trade.exit_rule,
              exit_reason: trade.exit_reason
            }
          };
          markers.push(exitMarker);
        }
      });

      console.log(`🎯 Setting ${markers.length} markers on chart`);
      try {
        candlestickSeries.setMarkers(markers);
        console.log('✅ Successfully set markers');
      } catch (error) {
        console.error('❌ Error setting markers:', error);
      }
    }

    // Set volume data
    volumeSeries.setData(
      data.map(item => ({
        time: item.time,
        value: item.volume,
        color: item.close > item.open ? '#26a69a55' : '#ef535055'
      }))
    );

    // Update volume visibility
    volumeSeries.applyOptions({ visible: showVolume });

    // Add SMA indicators to main chart
    if (mainChartIndicators.SMA && Array.isArray(mainChartIndicators.SMA)) {
      try {
        // Clear existing SMA series
        Object.keys(mainChartSeriesRef.current).forEach(id => {
          if (mainChartSeriesRef.current[id]?.type === 'SMA' && mainChartSeriesRef.current[id]?.series) {
            try {
              chart.removeSeries(mainChartSeriesRef.current[id].series);
              delete mainChartSeriesRef.current[id];
            } catch (error) {
              console.warn('Error removing SMA series:', error);
            }
          }
        });

        // Add new SMA series
        mainChartIndicators.SMA.forEach((indicator, index) => {
          if (!indicator || !indicator.parameters || typeof indicator.parameters.period !== 'number') {
            console.warn('Invalid SMA indicator:', indicator);
            return;
          }

          try {
            const series = chart.addLineSeries({
              color: getIndicatorColor(index),
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              title: '', // Remove the indicator title from the price scale
              priceLineVisible: false, // Remove the dotted price line
              lastValueVisible: false, // Hide the last value from the price scale
            });

            const smaData = calculateSMA(data, indicator.parameters.period, indicator.source || 'close', existingIndicatorData);
            if (smaData && Array.isArray(smaData)) {
              series.setData(smaData);
              mainChartSeriesRef.current[indicator.id] = {
                type: 'SMA',
                series: series
              };
            } else {
              console.warn('Invalid SMA data calculated:', smaData);
              chart.removeSeries(series);
            }
          } catch (error) {
            console.error('Error adding SMA series:', error);
          }
        });
      } catch (error) {
        console.error('Error updating SMA indicators:', error);
      }
    }

    // Add EMA indicators to main chart
    if (mainChartIndicators.EMA && Array.isArray(mainChartIndicators.EMA)) {
      try {
        // Clear existing EMA series
        Object.keys(mainChartSeriesRef.current).forEach(id => {
          if (mainChartSeriesRef.current[id]?.type === 'EMA' && mainChartSeriesRef.current[id]?.series) {
            try {
              chart.removeSeries(mainChartSeriesRef.current[id].series);
              delete mainChartSeriesRef.current[id];
            } catch (error) {
              console.warn('Error removing EMA series:', error);
            }
          }
        });

        // Add new EMA series
        mainChartIndicators.EMA.forEach((indicator, index) => {
          if (!indicator || !indicator.parameters || typeof indicator.parameters.period !== 'number') {
            console.warn('Invalid EMA indicator:', indicator);
            return;
          }

          try {
            const series = chart.addLineSeries({
              color: getIndicatorColor(index + (mainChartIndicators.SMA?.length || 0)),
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              title: '', // Remove the indicator title from the price scale
              priceLineVisible: false, // Remove the dotted price line
              lastValueVisible: false, // Hide the last value from the price scale
            });

            const emaData = calculateEMA(data, indicator.parameters.period, indicator.source || 'close', existingIndicatorData);
            if (emaData && Array.isArray(emaData)) {
              series.setData(emaData);
              mainChartSeriesRef.current[indicator.id] = {
                type: 'EMA',
                series: series
              };
            } else {
              console.warn('Invalid EMA data calculated:', emaData);
              chart.removeSeries(series);
            }
          } catch (error) {
            console.error('Error adding EMA series:', error);
          }
        });
      } catch (error) {
        console.error('Error updating EMA indicators:', error);
      }
    }

    // Add Bollinger Bands to main chart
    if (mainChartIndicators.BollingerBands && Array.isArray(mainChartIndicators.BollingerBands)) {
      try {
        // Clear existing Bollinger Bands series
        Object.keys(mainChartSeriesRef.current).forEach(id => {
          if (mainChartSeriesRef.current[id]?.type === 'BollingerBands' && mainChartSeriesRef.current[id]?.series) {
            try {
              mainChartSeriesRef.current[id].series.forEach(series => {
                chart.removeSeries(series);
              });
              delete mainChartSeriesRef.current[id];
            } catch (error) {
              console.warn('Error removing Bollinger Bands series:', error);
            }
          }
        });

        // Add new Bollinger Bands series
        mainChartIndicators.BollingerBands.forEach((indicator, index) => {
          if (!indicator || !indicator.parameters) {
            console.warn('Invalid Bollinger Bands indicator:', indicator);
            return;
          }

          try {
            const bbData = calculateBollingerBands(
              data,
              indicator.parameters.period || 20,
              indicator.parameters.devfactor || 2,
              indicator.parameters.offset || 0,
              indicator.source || 'close',
              existingIndicatorData
            );

            if (!bbData || !Array.isArray(bbData)) {
              console.warn('Invalid Bollinger Bands data calculated:', bbData);
              return;
            }

            // Create middle band (SMA)
            const middleSeries = chart.addLineSeries({
              color: getIndicatorColor(index),
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              title: '', // Remove the indicator title from the price scale
              priceLineVisible: false, // Remove the dotted price line
              lastValueVisible: false, // Hide the last value from the price scale
            });

            // Create upper band
            const upperSeries = chart.addLineSeries({
              color: getIndicatorColor(index),
              lineWidth: 1,
              lineStyle: 2, // Dashed line
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              priceLineVisible: false, // Remove the dotted price line
              lastValueVisible: false, // Hide the last value from the price scale
            });

            // Create lower band
            const lowerSeries = chart.addLineSeries({
              color: getIndicatorColor(index),
              lineWidth: 1,
              lineStyle: 2, // Dashed line
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              priceLineVisible: false, // Remove the dotted price line
              lastValueVisible: false, // Hide the last value from the price scale
            });

            // Set data for all bands
            middleSeries.setData(bbData.map(d => ({ time: d.time, value: d.middle })));
            upperSeries.setData(bbData.map(d => ({ time: d.time, value: d.upper })));
            lowerSeries.setData(bbData.map(d => ({ time: d.time, value: d.lower })));

            // Store references
            mainChartSeriesRef.current[indicator.id] = {
              type: 'BollingerBands',
              series: [middleSeries, upperSeries, lowerSeries]
            };
          } catch (error) {
            console.error('Error adding Bollinger Bands series:', error);
          }
        });
      } catch (error) {
        console.error('Error updating Bollinger Bands indicators:', error);
      }
    }

    // Add Support & Resistance to main chart
    if (mainChartIndicators.SupportResistance && Array.isArray(mainChartIndicators.SupportResistance)) {
      console.log('Processing Support & Resistance indicators:', mainChartIndicators.SupportResistance);
      try {
        // Clear existing Support & Resistance series
        Object.keys(mainChartSeriesRef.current).forEach(id => {
          if (mainChartSeriesRef.current[id]?.type === 'SupportResistance' && mainChartSeriesRef.current[id]?.series) {
            try {
              mainChartSeriesRef.current[id].series.forEach(series => {
                chart.removeSeries(series);
              });
              delete mainChartSeriesRef.current[id];
            } catch (error) {
              console.warn('Error removing Support & Resistance series:', error);
            }
          }
        });

        // Configure the price scale to hide support and resistance levels
        chart.priceScale('right').applyOptions({
          autoScale: true,
          mode: 0, // Normal mode (not logarithmic)
          entireTextOnly: false, // Allow partial text to ensure all price levels are visible
          // This is the key setting to hide support and resistance levels from the price scale
          tickMarkFormatter: (price) => {
            // Only show regular price levels, not support/resistance levels
            // We'll identify support/resistance levels by checking if they match any of our calculated levels
            const isSupportResistanceLevel = srLevels.some(level =>
              Math.abs(level.value - price) < 0.00001 // Very small tolerance
            );

            // If it's a support/resistance level, return an empty string to hide it
            if (isSupportResistanceLevel) {
              return '';
            }

            // Otherwise, format the price normally
            return price.toFixed(5);
          },
        });

        // Add new Support & Resistance series
        mainChartIndicators.SupportResistance.forEach((indicator, index) => {
          if (!indicator || !indicator.parameters) {
            console.warn('Invalid Support & Resistance indicator:', indicator);
            return;
          }

          try {
            // Get the left and right lookback periods from the indicator parameters
            const leftBars = parseInt(indicator.parameters.left) || 10;
            const rightBars = parseInt(indicator.parameters.right) || 10;

            console.log(`Calculating Support & Resistance with leftBars=${leftBars}, rightBars=${rightBars}`);

            // First, clear any existing Support & Resistance series
            if (mainChartSeriesRef.current[indicator.id]) {
              const existingSeries = mainChartSeriesRef.current[indicator.id].series;
              if (Array.isArray(existingSeries)) {
                existingSeries.forEach(series => {
                  try {
                    chart.removeSeries(series);
                  } catch (error) {
                    console.warn('Error removing existing S&R series:', error);
                  }
                });
              }
              delete mainChartSeriesRef.current[indicator.id];
            }

            // Calculate new Support & Resistance levels
            const srLevels = calculateSupportResistance(
              data,
              leftBars,
              rightBars
            );

            if (!srLevels || !Array.isArray(srLevels)) {
              console.warn('Invalid Support & Resistance data calculated:', srLevels);
              return;
            }

            // Log detailed information about the levels found
            console.log(`Found ${srLevels.length} support and resistance levels:`,
              srLevels.map(level => ({
                type: level.type,
                value: level.value.toFixed(5),
                time: new Date(level.time * 1000).toISOString(),
                strength: level.strength
              }))
            );

            // Log the price range of the data for comparison
            const minPrice = Math.min(...data.map(d => d.low));
            const maxPrice = Math.max(...data.map(d => d.high));
            console.log(`Price range in data: ${minPrice.toFixed(5)} - ${maxPrice.toFixed(5)}`);

            // Log support and resistance counts separately
            const supportCount = srLevels.filter(level => level.type === 'support').length;
            const resistanceCount = srLevels.filter(level => level.type === 'resistance').length;
            console.log(`Support levels: ${supportCount}, Resistance levels: ${resistanceCount}`);

            // Separate support and resistance levels
            const supportLevels = srLevels.filter(level => level.type === 'support');
            const resistanceLevels = srLevels.filter(level => level.type === 'resistance');

            // Create a separate series for each support and resistance level
            // Store all created series to reference them later
            const allSeries = [];

            // Find the first and last time in the data
            const firstTime = data[0].time;
            const lastTime = data[data.length - 1].time;

            // Create a series for each level
            srLevels.forEach((level, index) => {
              // Set color based on type - blue for support, red for resistance
              // Using more vibrant colors with higher opacity for better visibility
              const color = level.type === 'support' ? '#2962FF' : '#FF3B30'; // Bright blue for support, Bright red for resistance

              // Create a more descriptive title that includes the price level and strength
              const formattedPrice = level.value.toFixed(5);
              const title = level.type === 'support'
                ? `Support (${index + 1}) - ${formattedPrice} [Strength: ${level.strength}]`
                : `Resistance (${index + 1}) - ${formattedPrice} [Strength: ${level.strength}]`;

              // Instead of drawing a line across the entire chart, we'll create a marker at the swing point
              // and a short horizontal line extending from it

              // Find the candle index where this level was detected
              const pivotCandleIndex = level.candleIndex || 0;
              const pivotTime = data[pivotCandleIndex]?.time || firstTime;

              // Calculate a much shorter range for the line - just around the pivot point
              const isRecentLevel = level.isRecent || pivotCandleIndex > data.length * 0.7;

              // Make lines shorter - just a few candles before and after the pivot
              const lineStartIndex = Math.max(0, pivotCandleIndex - 3);
              const lineEndIndex = Math.min(data.length - 1, pivotCandleIndex + 5);

              const lineStartTime = data[lineStartIndex]?.time || firstTime;
              const lineEndTime = data[lineEndIndex]?.time || lastTime;

              // Log the level value and time range
              console.log(`Drawing ${level.type} at price ${level.value.toFixed(5)} from candle ${lineStartIndex} to ${lineEndIndex}`);

              // Create a line series for this level with improved visibility
              const lineSeries = chart.addLineSeries({
                color: color,
                lineWidth: isRecentLevel ? 2 : 1, // Thicker lines for recent levels
                lineStyle: isRecentLevel ? 0 : 2, // Solid for recent, dashed for older
                priceFormat: {
                  type: 'price',
                  precision: 5,
                  minMove: 0.00001,
                },
                title: '', // Remove the indicator title from the price scale
                lastValueVisible: false, // Hide the last value from the price scale
                priceLineVisible: false,
                lastPriceAnimation: 0,
                priceScaleId: 'right',
                baseLineVisible: false,
                visible: true,
              });

              // Draw the line only around the swing point
              lineSeries.setData([
                { time: lineStartTime, value: level.value },
                { time: lineEndTime, value: level.value }
              ]);

              // Add a marker at the exact swing point
              const markerSeries = chart.addLineSeries({
                color: 'transparent',
                lastValueVisible: false,
                priceLineVisible: false,
                priceScaleId: 'right',
              });

              // Set a single marker at the pivot point with improved visibility
              markerSeries.setMarkers([
                {
                  time: pivotTime,
                  position: level.type === 'support' ? 'belowBar' : 'aboveBar',
                  color: color,
                  shape: level.type === 'support' ? 'circle' : 'circle',
                  text: level.type === 'support' ? `S${index+1}: ${level.value.toFixed(5)}` : `R${index+1}: ${level.value.toFixed(5)}`,
                  size: 3
                }
              ]);

              // Add a second marker to make it more visible
              const markerSeries2 = chart.addLineSeries({
                color: 'transparent',
                lastValueVisible: false,
                priceLineVisible: false,
                priceScaleId: 'right',
              });

              // Add a more visible marker at the pivot point
              markerSeries2.setMarkers([
                {
                  time: pivotTime,
                  position: level.type === 'support' ? 'belowBar' : 'aboveBar',
                  color: level.type === 'support' ? 'rgba(41, 98, 255, 0.9)' : 'rgba(255, 59, 48, 0.9)',
                  shape: level.type === 'support' ? 'arrowUp' : 'arrowDown',
                  size: 2
                }
              ]);

              // Add a third marker to highlight the exact candle
              const markerSeries3 = chart.addLineSeries({
                color: 'transparent',
                lastValueVisible: false,
                priceLineVisible: false,
                priceScaleId: 'right',
              });

              // Add a dot at the exact price level on the pivot candle
              markerSeries3.setMarkers([
                {
                  time: pivotTime,
                  position: 'inBar',
                  color: level.type === 'support' ? 'rgba(41, 98, 255, 0.7)' : 'rgba(255, 59, 48, 0.7)',
                  shape: 'circle',
                  size: 1
                }
              ]);

              // Store both additional series
              allSeries.push(markerSeries3);
              allSeries.push(markerSeries2);

              // Don't add price line markers as requested by the user
              // We'll rely on the markers and short horizontal lines instead

              // Store both series for later reference
              allSeries.push(lineSeries);
              allSeries.push(markerSeries);
            });

            // Store references to all created series
            mainChartSeriesRef.current[indicator.id] = {
              type: 'SupportResistance',
              series: allSeries
            };
          } catch (error) {
            console.error('Error adding Support & Resistance series:', error);
          }
        });
      } catch (error) {
        console.error('Error updating Support & Resistance indicators:', error);
      }
    }

    // Create and update RSI charts with source data
    Object.entries(indicatorGroups).forEach(([groupId, group]) => {
      if (!group || !group.indicators) {
        console.warn('Invalid group:', group);
        return;
      }

      const indicatorType = group.indicators[0]?.type;
      const chart = createIndicatorChart(groupId, indicatorType === 'MACD' ? 250 : 200, indicatorType);
      if (!chart) return;

      try {
        // First, calculate and store data for main indicators
        group.indicators.forEach(indicator => {
          let indicatorData;
          if (indicator.type === 'RSI') {
            indicatorData = calculateRSI(
              data,
              indicator.parameters.period || 14,
              indicator.source || 'close',
              existingIndicatorData
            );
          } else if (indicator.type === 'MACD') {
            indicatorData = calculateMACD(data, {
              fast: indicator.parameters.fast || 12,
              slow: indicator.parameters.slow || 26,
              signal: indicator.parameters.signal || 9
            });
          } else if (indicator.type === 'ATR') {
            indicatorData = calculateATR(data, indicator.parameters.period || 14);
          }

          if (indicatorData && Array.isArray(indicatorData)) {
            existingIndicatorData[indicator.id] = indicatorData;
          }
        });

        // Then update the series for main indicators
        if (indicatorType === 'RSI') {
          group.indicators.forEach((indicator, index) => {
            if (!indicator || !indicator.parameters) {
              console.warn('Invalid RSI indicator:', indicator);
              return;
            }

            const series = chart.addLineSeries({
              color: getIndicatorColor(index),
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 0, // Show whole numbers
                minMove: 1,
              },
              priceScaleId: 'rsi-scale', // Use a dedicated scale for RSI
              priceLineVisible: true, // Show the dotted price line
              lastValueVisible: false, // Hide the last value from the price scale
              title: '', // Remove the indicator title from the price scale
            });

            // Configure the price scale for RSI - using the same approach as the main chart
            chart.priceScale('rsi-scale').applyOptions({
              autoScale: true,
              mode: 0, // Normal mode
              invertScale: false,
              alignLabels: true,
              borderVisible: true,
              borderColor: '#1E222D',
              scaleMargins: {
                top: 0.1,
                bottom: 0.1,
              },
              entireTextOnly: true, // Prevent partial text to avoid duplicates
              ticksVisible: true,
              // Force the price range to include 0, 30, 70, and 100
              priceRange: {
                minValue: 0,
                maxValue: 100,
              },
            });

            const indicatorData = existingIndicatorData[indicator.id];
            if (indicatorData && Array.isArray(indicatorData)) {
              series.setData(indicatorData);

              // Update the price line with the current RSI value
              if (indicatorData.length > 0) {
                const currentRSI = indicatorData[indicatorData.length - 1].value;

                try {
                  // Add a price line for the current RSI value
                  series.createPriceLine({
                    price: currentRSI,
                    color: getIndicatorColor(index),
                    lineWidth: 1,
                    lineStyle: 0, // Solid line
                    axisLabelVisible: true, // Show the label on the axis
                    title: '',
                  });
                } catch (error) {
                  console.warn('Error creating price line for RSI:', error);
                }
              }

              indicatorSeriesRef.current[indicator.id] = series;
            } else {
              console.warn('Invalid RSI data:', indicatorData);
              chart.removeSeries(series);
            }
          });

          // Add overbought and oversold lines with price lines
          const overboughtLine = chart.addLineSeries({
            color: '#787B86',
            lineWidth: 1,
            lineStyle: 1, // Dashed line
            priceScaleId: 'rsi-scale', // Use the same scale as RSI
            lastValueVisible: true, // Hide the last value from the price scale
            priceLineVisible: false, // Hide the price line
            title: '', // Remove the title
          });

          const oversoldLine = chart.addLineSeries({
            color: '#787B86',
            lineWidth: 1,
            lineStyle: 1, // Dashed line
            priceScaleId: 'rsi-scale', // Use the same scale as RSI
            lastValueVisible: true, // Hide the last value from the price scale
            priceLineVisible: false, // Hide the price line
            title: '', // Remove the title
          });

          // Create data for the overbought line (70)
          const overboughtData = data.map((point, index) => ({
            time: point.time,
            value: 70
          }));

          // Create data for the oversold line (30)
          const oversoldData = data.map((point, index) => ({
            time: point.time,
            value: 30
          }));
          overboughtLine.setData(overboughtData);
          oversoldLine.setData(oversoldData);
        } else if (indicatorType === 'MACD') {
          group.indicators.forEach((indicator) => {
            if (!indicator || !indicator.parameters) {
              console.warn('Invalid MACD indicator:', indicator);
              return;
            }

            try {
              const indicatorData = existingIndicatorData[indicator.id];
              if (!indicatorData || !Array.isArray(indicatorData)) {
                console.warn('Invalid MACD data:', indicatorData);
                return;
              }

              // Add histogram series
              const histogramSeries = chart.addHistogramSeries({
                color: '#26a69a',
                priceFormat: {
                  type: 'custom',
                  minMove: 0.00001,
                  formatter: (price) => price?.toFixed(5) || '0.00000',
                },
                priceScaleId: 'macd-scale', // Use a dedicated scale for MACD
              });

              // Configure the price scale for MACD to reduce tick marks
              chart.priceScale('macd-scale').applyOptions({
                autoScale: true,
                mode: 0, // Normal mode
                invertScale: false,
                alignLabels: true,
                borderVisible: true,
                borderColor: '#1E222D',
                scaleMargins: {
                  top: 0.1,
                  bottom: 0.1,
                },
                entireTextOnly: false,
                ticksVisible: true,
                // Reduce the number of tick marks
                tickMarkFormatter: (price) => {
                  // Only show a limited number of decimal places
                  const rounded = Math.round(price * 10000) / 10000;
                  // Only show some tick marks to reduce clutter
                  return rounded === 0 || Math.abs(rounded) >= 0.001 ? rounded.toFixed(4) : '';
                },
              });

              // Add MACD line
              const macdSeries = chart.addLineSeries({
                color: '#2962FF',
                lineWidth: 2,
                priceFormat: {
                  type: 'custom',
                  minMove: 0.00001,
                  formatter: (price) => price?.toFixed(5) || '0.00000',
                },
                priceScaleId: 'macd-scale', // Use the same scale as histogram
                priceLineVisible: false, // Remove the dotted price line
                lastValueVisible: true, // Hide the last value from the price scale
                title: '', // Remove the indicator title from the price scale
              });

              // Add signal line
              const signalSeries = chart.addLineSeries({
                color: '#FF5252',
                lineWidth: 2,
                priceFormat: {
                  type: 'custom',
                  minMove: 0.00001,
                  formatter: (price) => price?.toFixed(5) || '0.00000',
                },
                priceScaleId: 'macd-scale', // Use the same scale as histogram
                priceLineVisible: false, // Remove the dotted price line
                lastValueVisible: true, // Hide the last value from the price scale
                title: '', // Remove the indicator title from the price scale
              });

              // Set data for all series
              histogramSeries.setData(
                indicatorData.map(d => ({
                  time: d.time,
                  value: d.histogram,
                  color: d.histogram >= 0 ? '#26a69a55' : '#ef535055'
                }))
              );

              macdSeries.setData(
                indicatorData.map(d => ({
                  time: d.time,
                  value: d.macd
                }))
              );

              signalSeries.setData(
                indicatorData.map(d => ({
                  time: d.time,
                  value: d.signal
                }))
              );

              // Store references
              indicatorSeriesRef.current[indicator.id] = {
                histogram: histogramSeries,
                macd: macdSeries,
                signal: signalSeries
              };
            } catch (error) {
              console.error('Error adding MACD series:', error);
            }
          });
        } else if (indicatorType === 'ATR') {
          group.indicators.forEach((indicator, index) => {
            if (!indicator || !indicator.parameters) {
              console.warn('Invalid ATR indicator:', indicator);
              return;
            }

            const series = chart.addLineSeries({
              color: '#FF5252', // Red for ATR
              lineWidth: 2,
              priceFormat: {
                type: 'custom',
                minMove: 0.00001,
                formatter: (price) => price?.toFixed(5) || '0.00000',
              },
              priceScaleId: 'atr-scale', // Use a dedicated scale for ATR
              priceLineVisible: true, // Remove the dotted price line
              lastValueVisible: true, // Hide the last value from the price scale
              title: '', // Remove the indicator title from the price scale
            });

            // Configure the price scale for ATR to reduce tick marks
            chart.priceScale('atr-scale').applyOptions({
              autoScale: true,
              mode: 0, // Normal mode
              invertScale: false,
              alignLabels: true,
              borderVisible: true,
              borderColor: '#1E222D',
              scaleMargins: {
                top: 0.1,
                bottom: 0.1,
              },
              entireTextOnly: false,
              ticksVisible: true,
              // Reduce the number of tick marks
              tickMarkFormatter: (price) => {
                // Only show a limited number of decimal places
                const rounded = Math.round(price * 10000) / 10000;
                // Only show some tick marks to reduce clutter
                return rounded === 0 || Math.abs(rounded) >= 0.0005 ? rounded.toFixed(5) : '';
              },
            });

            const indicatorData = existingIndicatorData[indicator.id];
            if (indicatorData && Array.isArray(indicatorData)) {
              series.setData(indicatorData);
              indicatorSeriesRef.current[indicator.id] = series;
            } else {
              console.warn('Invalid ATR data:', indicatorData);
              chart.removeSeries(series);
            }
          });
        }

        // Finally, update SMAs and Bollinger Bands for this indicator group
        if (group.smaIndicators && Array.isArray(group.smaIndicators)) {
          group.smaIndicators.forEach((smaIndicator, index) => {
            if (!smaIndicator || !smaIndicator.parameters) {
              console.warn('Invalid SMA indicator:', smaIndicator);
              return;
            }

            const series = chart.addLineSeries({
              color: getIndicatorColor(index + group.indicators.length),
              lineWidth: 2,
              priceFormat: {
                type: 'custom',
                minMove: 0.00001,
                formatter: (price) => price?.toFixed(5) || '0.00000',
              },
              priceLineVisible: true, // Remove the dotted price line
              lastValueVisible: true, // Hide the last value from the price scale
              title: '', // Remove the indicator title from the price scale
            });

            const smaData = calculateSMA(
              data,
              smaIndicator.parameters.period,
              smaIndicator.source,
              existingIndicatorData
            );

            if (smaData && Array.isArray(smaData)) {
              series.setData(smaData);
              indicatorSeriesRef.current[smaIndicator.id] = series;

              // Update legend
              const sourceLabel = getSourceLabel(smaIndicator.source, indicators);
              series.applyOptions({
                title: `SMA(${smaIndicator.parameters.period}) of ${sourceLabel}`
              });
            } else {
              console.warn('Invalid SMA data:', smaData);
              chart.removeSeries(series);
            }
          });
        }

        if (group.bollingerIndicators && Array.isArray(group.bollingerIndicators)) {
          group.bollingerIndicators.forEach((bbIndicator, index) => {
            if (!bbIndicator || !bbIndicator.parameters) {
              console.warn('Invalid Bollinger Bands indicator:', bbIndicator);
              return;
            }

            const bbData = calculateBollingerBands(
              data,
              bbIndicator.parameters.period || 20,
              bbIndicator.parameters.devfactor || 2,
              bbIndicator.parameters.offset || 0,
              bbIndicator.source,
              existingIndicatorData
            );

            if (!bbData || !Array.isArray(bbData)) {
              console.warn('Invalid Bollinger Bands data:', bbData);
              return;
            }

            // Create middle band (SMA)
            const middleSeries = chart.addLineSeries({
              color: getIndicatorColor(index + group.indicators.length),
              lineWidth: 2,
              priceFormat: {
                type: 'custom',
                minMove: 0.00001,
                formatter: (price) => price?.toFixed(5) || '0.00000',
              },
              priceLineVisible: true, // Remove the dotted price line
              lastValueVisible: true, // Hide the last value from the price scale
              title: '', // Remove the indicator title from the price scale
            });

            // Create upper band
            const upperSeries = chart.addLineSeries({
              color: getIndicatorColor(index + group.indicators.length),
              lineWidth: 1,
              lineStyle: 2, // Dashed line
              priceFormat: {
                type: 'custom',
                minMove: 0.00001,
                formatter: (price) => price?.toFixed(5) || '0.00000',
              },
              priceLineVisible: true, // Remove the dotted price line
              lastValueVisible: true, // Hide the last value from the price scale
              title: '', // Remove the indicator title from the price scale
            });

            // Create lower band
            const lowerSeries = chart.addLineSeries({
              color: getIndicatorColor(index + group.indicators.length),
              lineWidth: 1,
              lineStyle: 2, // Dashed line
              priceFormat: {
                type: 'custom',
                minMove: 0.00001,
                formatter: (price) => price?.toFixed(5) || '0.00000',
              },
              priceLineVisible: true, // Remove the dotted price line
              lastValueVisible: true, // Hide the last value from the price scale
              title: '', // Remove the indicator title from the price scale
            });

            // Set data for all bands
            middleSeries.setData(bbData.map(d => ({ time: d.time, value: d.middle })));
            upperSeries.setData(bbData.map(d => ({ time: d.time, value: d.upper })));
            lowerSeries.setData(bbData.map(d => ({ time: d.time, value: d.lower })));

            // Store references
            indicatorSeriesRef.current[bbIndicator.id] = [middleSeries, upperSeries, lowerSeries];

            // Update legend
            const sourceLabel = getSourceLabel(bbIndicator.source, indicators);
            middleSeries.applyOptions({
              title: `BB(${bbIndicator.parameters.period}, ${bbIndicator.parameters.devfactor}) of ${sourceLabel}`
            });
          });
        }

        // Sync with main chart
        chart.timeScale().subscribeVisibleTimeRangeChange((range) => {
          if (range && mainChartInstanceRef.current) {
            mainChartInstanceRef.current.timeScale().setVisibleRange(range);
          }
        });

        if (mainChartInstanceRef.current) {
          mainChartInstanceRef.current.timeScale().subscribeVisibleTimeRangeChange((range) => {
            if (range) {
              chart.timeScale().setVisibleRange(range);
            }
          });
        }
      } catch (error) {
        console.error('Error updating indicator chart:', error);
      }
    });

    // Fit content
    chart.timeScale().fitContent();

    // Update the volume toggle button
    updateVolumeToggle();

  }, [data, selectedTradingSessions, showVolume, updateVolumeToggle, indicatorGroups, mainChartIndicators, createIndicatorChart, calculateRSI, calculateSMA, calculateEMA, calculateMACD, calculateBollingerBands, calculateATR, trades]);

  // Helper function to get different colors for multiple indicators
  const getIndicatorColor = (index) => {
    const colors = ['#2962FF', '#FF5252', '#FFB300', '#00C853', '#AA00FF'];
    return colors[index % colors.length];
  };

  // Update charts when data or sessions change
  useEffect(() => {
    console.log('Effect triggered - updating charts');
    if (data && selectedTradingSessions) {
      // Log the first and last candle for debugging
      if (data.length > 0) {
        const firstCandle = data[0];
        const lastCandle = data[data.length - 1];

        console.log('Chart first candle:', firstCandle);
        console.log('Chart first candle UTC time:', new Date(firstCandle.time * 1000).toUTCString());

        console.log('Chart last candle:', lastCandle);
        console.log('Chart last candle UTC time:', new Date(lastCandle.time * 1000).toUTCString());
        console.log('Chart last candle UTC hours:', new Date(lastCandle.time * 1000).getUTCHours());
        console.log('Chart last candle UTC minutes:', new Date(lastCandle.time * 1000).getUTCMinutes());
      }

      updateCharts();
    }
  }, [data, selectedTradingSessions, updateCharts]);

  // Setup resize observer
  useEffect(() => {
    const handleResize = () => {
      if (mainChartRef.current && mainChartInstanceRef.current) {
        mainChartInstanceRef.current.applyOptions({
          width: mainChartRef.current.clientWidth,
        });
      }

      // Properly resize each indicator chart with its own container
      Object.entries(indicatorChartsRef.current).forEach(([groupId, el]) => {
        if (el && indicatorChartInstancesRef.current[groupId]) {
          indicatorChartInstancesRef.current[groupId].applyOptions({
            width: el.clientWidth,
          });
        }
      });
    };

    // Create a ResizeObserver to handle container size changes
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        // Check if this is the main chart container
        if (entry.target === mainChartRef.current && mainChartInstanceRef.current) {
          mainChartInstanceRef.current.applyOptions({
            width: entry.contentRect.width,
          });
          console.log('Main chart resized to width:', entry.contentRect.width);
        }

        // Check if this is an indicator chart container
        Object.entries(indicatorChartsRef.current).forEach(([groupId, el]) => {
          if (entry.target === el && indicatorChartInstancesRef.current[groupId]) {
            indicatorChartInstancesRef.current[groupId].applyOptions({
              width: entry.contentRect.width,
            });
            console.log(`Indicator chart ${groupId} resized to width:`, entry.contentRect.width);
          }
        });
      }
    });

    // Observe the main chart container
    if (mainChartRef.current) {
      resizeObserver.observe(mainChartRef.current);
    }

    // Observe all indicator chart containers
    Object.values(indicatorChartsRef.current).forEach(el => {
      if (el) {
        resizeObserver.observe(el);
      }
    });

    // Also observe parent containers to catch resize events
    let parent = mainChartRef.current?.parentElement;
    while (parent) {
      resizeObserver.observe(parent);
      parent = parent.parentElement;
      // Stop at the body to avoid observing too many elements
      if (parent === document.body) break;
    }

    // Also listen to window resize events as a fallback
    const windowResizeHandler = () => {
      // Add a small delay to ensure DOM has updated
      setTimeout(handleResize, 100);
    };
    window.addEventListener('resize', windowResizeHandler);

    // Store the observer reference for cleanup
    resizeObserverRef.current = resizeObserver;

    // Force a resize after initial render
    setTimeout(handleResize, 200);

    return () => {
      // Disconnect the observer and remove event listener on cleanup
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      window.removeEventListener('resize', windowResizeHandler);
    };
  }, []);

  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-red-400">{error}</span>
        </div>
      </div>
    );
  }

  // Update the MainChartLegend component to handle ATR
  const MainChartLegend = () => {
    const hasIndicators = (
      mainChartIndicators.SMA?.length > 0 ||
      mainChartIndicators.EMA?.length > 0 ||
      mainChartIndicators.BollingerBands?.length > 0 ||
      mainChartIndicators.SupportResistance?.length > 0
    );

    if (!hasIndicators) return null;

    return (
      <div className="absolute left-3 top-3 z-10 flex flex-wrap gap-2">
        {mainChartIndicators.SMA?.map((indicator, index) => (
          <div
            key={indicator.id}
            className="px-2 py-1 rounded bg-[#1a1a1a]/80 backdrop-blur-sm border border-[#2a2a2a] flex items-center gap-2"
          >
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: getIndicatorColor(index) }}
            />
            <span className="text-sm font-medium text-[#FEFEFF]">
              SMA({indicator.parameters.period})
              {indicator.source !== 'close' && (
                <span className="text-[#FEFEFF]/60 ml-1">
                  on {getSourceLabel(indicator.source, indicators)}
                </span>
              )}
            </span>
          </div>
        ))}
        {mainChartIndicators.EMA?.map((indicator, index) => (
          <div
            key={indicator.id}
            className="px-2 py-1 rounded bg-[#1a1a1a]/80 backdrop-blur-sm border border-[#2a2a2a] flex items-center gap-2"
          >
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: getIndicatorColor(index + (mainChartIndicators.SMA?.length || 0)) }}
            />
            <span className="text-sm font-medium text-[#FEFEFF]">
              EMA({indicator.parameters.period})
              {indicator.source !== 'close' && (
                <span className="text-[#FEFEFF]/60 ml-1">
                  on {getSourceLabel(indicator.source, indicators)}
                </span>
              )}
            </span>
          </div>
        ))}
        {mainChartIndicators.BollingerBands?.map((indicator, index) => (
          <div
            key={indicator.id}
            className="px-2 py-1 rounded bg-[#1a1a1a]/80 backdrop-blur-sm border border-[#2a2a2a] flex items-center gap-2"
          >
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: getIndicatorColor(index + (mainChartIndicators.SMA?.length || 0) + (mainChartIndicators.EMA?.length || 0)) }}
            />
            <span className="text-sm font-medium text-[#FEFEFF]">
              BB({indicator.parameters.period}, {indicator.parameters.devfactor})
              {indicator.source !== 'close' && (
                <span className="text-[#FEFEFF]/60 ml-1">
                  on {getSourceLabel(indicator.source, indicators)}
                </span>
              )}
            </span>
          </div>
        ))}
        {mainChartIndicators.SupportResistance?.map((indicator, index) => (
          <div
            key={indicator.id}
            className="px-2 py-1 rounded bg-[#1a1a1a]/80 backdrop-blur-sm border border-[#2a2a2a] flex items-center gap-2"
          >
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-[#2962FF]" />
              <span className="text-sm font-medium text-[#FEFEFF]">Support</span>
            </div>
            <div className="flex items-center gap-2 ml-2">
              <div className="w-3 h-3 rounded-full bg-[#ef5350]" />
              <span className="text-sm font-medium text-[#FEFEFF]">Resistance</span>
            </div>
            <span className="text-xs text-[#FEFEFF]/60 ml-1">
              ({indicator.parameters.left}, {indicator.parameters.right})
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Update the render method to include MACD legend
  const IndicatorLegend = ({ groupId, indicators, type }) => {
    if (!indicators?.length) return null;

    const getSourceLabel = (source) => {
      if (!source) return 'close';
      // Check if source is an indicator ID
      const sourceIndicator = [...(mainChartIndicators.SMA || []), ...Object.values(indicatorGroups).flat()]
        .find(ind => ind.id === source);

      if (sourceIndicator) {
        return `${sourceIndicator.type}(${Object.values(sourceIndicator.parameters).join(', ')})`;
      }
      return source.toLowerCase();
    };

    return (
      <div className="absolute left-3 top-3 z-10 flex flex-wrap gap-2">
        {type === 'MACD' ? (
          <div className="px-2 py-1 rounded bg-[#1a1a1a]/80 backdrop-blur-sm border border-[#2a2a2a]">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[#2962FF]" />
                <span className="text-sm font-medium text-[#FEFEFF]">MACD</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[#FF5252]" />
                <span className="text-sm font-medium text-[#FEFEFF]">Signal</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-3 rounded bg-[#26a69a55]" />
                <span className="text-sm font-medium text-[#FEFEFF]">Histogram</span>
              </div>
            </div>
            <div className="mt-1 text-xs text-[#FEFEFF]/60">
              {indicators.map(ind =>
                `(${ind.parameters.fast}, ${ind.parameters.slow}, ${ind.parameters.signal})`
              ).join(', ')}
            </div>
          </div>
        ) : (
          indicators.map((indicator, index) => (
            <div
              key={indicator.id}
              className="px-2 py-1 rounded bg-[#1a1a1a]/80 backdrop-blur-sm border border-[#2a2a2a] flex items-center gap-2"
            >
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: getIndicatorColor(index) }}
              />
              <span className="text-sm font-medium text-[#FEFEFF]">
                {type}({type === 'RSI' ? indicator.parameters.period :
                  Object.values(indicator.parameters).join(', ')})
                <span className="text-[#FEFEFF]/60 ml-1">
                  on {getSourceLabel(indicator.source)}
                </span>
              </span>
            </div>
          ))
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4 relative w-full overflow-hidden" style={{ minWidth: '300px', maxWidth: '100%' }}>
      {showPreferenceDialog && pendingIndicator && (
        <div className="fixed inset-0 flex items-center justify-center" style={{ zIndex: 9999 }}>
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={() => {
              setShowPreferenceDialog(false);
              setPendingIndicator(null);
              if (onIndicatorAdd) {
                onIndicatorAdd(false);
              }
            }}
          />
          <div className="relative bg-[#1a1a1a] rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl border border-[#2a2a2a]">
            <h3 className="text-xl font-bold text-[#FEFEFF] mb-4">
              Add {pendingIndicator.type} Indicator
            </h3>
            <p className="text-[#FEFEFF]/80 mb-6">
              How would you like to display the new {pendingIndicator.type}
              {pendingIndicator.type === 'RSI' && ` (${pendingIndicator.parameters.period})`}
              {pendingIndicator.type === 'MACD' &&
                ` (${pendingIndicator.parameters.fast}, ${pendingIndicator.parameters.slow}, ${pendingIndicator.parameters.signal})`
              } indicator?
            </p>
            <div className="space-y-4">
              {hasExistingWindows(pendingIndicator.type) && (
                <button
                  onClick={() => handleIndicatorPreference(pendingIndicator, 'existing')}
                  className="w-full px-4 py-3 bg-[#2a2a2a] hover:bg-[#3a3a3a] text-[#FEFEFF] rounded-lg transition-colors hover:shadow-lg"
                >
                  Add to existing {pendingIndicator.type} window
                </button>
              )}
              <button
                onClick={() => handleIndicatorPreference(pendingIndicator, 'new')}
                className="w-full px-4 py-3 bg-[#2a2a2a] hover:bg-[#3a3a3a] text-[#FEFEFF] rounded-lg transition-colors hover:shadow-lg"
              >
                Create new window
              </button>
              <button
                onClick={() => {
                  setShowPreferenceDialog(false);
                  setPendingIndicator(null);
                  if (onIndicatorAdd) {
                    onIndicatorAdd(false);
                  }
                }}
                className="w-full px-4 py-3 bg-red-500/10 hover:bg-red-500/20 text-red-500 rounded-lg transition-colors hover:shadow-lg"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="relative w-full" style={{ minWidth: '300px', maxWidth: '100%' }}>
        {isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-[#0A0B0B]/80 z-10">
            <div className="w-12 h-12 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin mb-4"></div>
            <div className="text-[#EFBD3A] text-sm">{loadingMessage}</div>
          </div>
        )}
        <MainChartLegend />
        <div
          ref={mainChartRef}
          className="w-full rounded-lg overflow-hidden"
          style={{
            height: '400px',
            position: 'relative',
            minWidth: '300px',
            maxWidth: '100%',
            resize: 'horizontal'
          }}
        />
      </div>

      {/* Render indicator charts */}
      {Object.entries(indicatorGroups).map(([groupId, groupIndicators]) => (
        <div key={groupId} className="relative w-full" style={{ minWidth: '300px', maxWidth: '100%' }}>
          <IndicatorLegend
            groupId={groupId}
            indicators={groupIndicators}
            type={groupIndicators[0]?.type}
          />
          <div
            ref={el => indicatorChartsRef.current[groupId] = el}
            className="w-full rounded-lg overflow-hidden"
            style={{
              height: groupIndicators[0]?.type === 'MACD' ? '250px' : '200px',
              position: 'relative',
              minWidth: '300px',
              maxWidth: '100%',
              resize: 'horizontal'
            }}
          >
            {/* Loading overlay for indicator charts */}
            {isLoading && (
              <div className="absolute inset-0 bg-[#0A0B0B]/80 backdrop-blur-sm flex items-center justify-center z-20">
                <div className="flex flex-col items-center space-y-4 bg-[#0A0B0B]/40 p-8 rounded-lg backdrop-blur-sm relative overflow-hidden">
                  {/* Animated gradient background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-[#0A0B0B]/0 via-[#EFBD3A]/10 to-[#0A0B0B]/0 -skew-x-12 animate-shimmer" />

                  {/* Main spinner */}
                  <div className="w-12 h-12 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin relative">
                    {/* Inner spinner */}
                    <div className="absolute inset-1 border-3 border-[#EFBD3A]/30 border-t-transparent rounded-full animate-spin-slow" />
                  </div>

                  <div className="text-sm text-[#FEFEFF]/80">Loading indicator data...</div>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}

      {/* Add animation styles */}
      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%) skewX(-12deg);
          }
          100% {
            transform: translateX(200%) skewX(-12deg);
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
        .animate-spin-slow {
          animation: spin 2s linear infinite;
        }
      `}</style>
    </div>
  );
};

export default StrategyChart;