/**
 * Real-time enhanced version of OptimizedTradingChart
 * Integrates WebSocket for live candle updates like TradingView
 */

import React, { useEffect, useRef, useState, useCallback, useMemo } from "react";
import OptimizedTradingChart from "./OptimizedTradingChart";
import { calculateIndicatorsFromStrategy } from "../utils/indicatorCalculations";
import { WEBSOCKET_URL } from "../config";

const RealTimeOptimizedTradingChart = ({
  candleData: initialCandleData,
  indicators: passedIndicators, // Rename to avoid confusion
  trades,
  timeframe: displayTimeframe,
  chartTimeframe = "1h",
  instrument,
  marketStatus,
  strategyInfo,
  strategy,
  enableRealTime = true,
  websocketUrl = WEBSOCKET_URL,
  ...otherProps
}) => {
  // State for real-time data
  const [candleData, setCandleData] = useState(initialCandleData || []);
  const [realTimeStatus, setRealTimeStatus] = useState('disconnected');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [updateCount, setUpdateCount] = useState(0);
  const [wsError, setWsError] = useState(null);
  const [isRealTimeUpdate, setIsRealTimeUpdate] = useState(false);

  // Stable empty object reference to prevent unnecessary re-renders
  const emptyIndicators = useMemo(() => ({}), []);

  // Calculate indicators from candle data in real-time
  const indicators = useMemo(() => {
    if (!candleData || candleData.length === 0 || !strategy) {
      return passedIndicators || emptyIndicators;
    }

    try {
      const calculatedIndicators = calculateIndicatorsFromStrategy(candleData, strategy);
      return calculatedIndicators;
    } catch (error) {
      console.error('❌ Error calculating indicators:', error);
      return passedIndicators || emptyIndicators;
    }
  }, [candleData, strategy]); // Removed passedIndicators from dependencies to prevent infinite loop

  // Refs
  const lastCandleRef = useRef(null);
  const isSubscribedRef = useRef(false);
  const wsRef = useRef(null);
  const mountedRef = useRef(true);
  const subscriptionKeyRef = useRef(null);
  const realTimeUpdateTimeoutRef = useRef(null);
  const jsonErrorCountRef = useRef(0);
  const lastJsonErrorTimeRef = useRef(0);

  // WebSocket connection state
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [isConnected, setIsConnected] = useState(false);

  // Connect to WebSocket
  const connectWebSocket = useCallback(() => {
    // Prevent multiple connections
    if (wsRef.current?.readyState === WebSocket.OPEN ||
        wsRef.current?.readyState === WebSocket.CONNECTING) {
      return;
    }

    console.log('🔗 Frontend attempting WebSocket connection to:', websocketUrl);
    setConnectionStatus('connecting');
    setRealTimeStatus('connecting');

    const ws = new WebSocket(websocketUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      console.log('✅ Frontend WebSocket connected successfully');
      setConnectionStatus('connected');
      setIsConnected(true);
      setRealTimeStatus('connected');
      setWsError(null);
    };

    ws.onmessage = (event) => {
      try {
        // Log raw message for debugging
        console.log('📥 Raw WebSocket data:', event.data);

        // Check if data is valid JSON
        if (!event.data || typeof event.data !== 'string') {
          console.warn('⚠️ Invalid WebSocket data type:', typeof event.data);
          return;
        }

        // Handle potential multiple JSON objects in one message
        // Try different splitting strategies
        let messages = [];

        // First try splitting by newlines
        const newlineSplit = event.data.trim().split('\n').filter(line => line.trim());
        if (newlineSplit.length > 1) {
          messages = newlineSplit;
        } else {
          // Try splitting by }{ pattern (concatenated JSON objects)
          const jsonSplit = event.data.trim().split('}{');
          if (jsonSplit.length > 1) {
            // Reconstruct proper JSON objects
            messages = jsonSplit.map((part, index) => {
              if (index === 0) return part + '}';
              if (index === jsonSplit.length - 1) return '{' + part;
              return '{' + part + '}';
            });
          } else {
            // Single message
            messages = [event.data.trim()];
          }
        }

        for (const messageStr of messages) {
          try {
            // Additional validation before parsing
            const trimmedMessage = messageStr.trim();
            if (!trimmedMessage) {
              console.warn('⚠️ Empty message, skipping');
              continue;
            }

            // Check if message starts and ends with valid JSON characters
            if (!trimmedMessage.startsWith('{') || !trimmedMessage.endsWith('}')) {
              console.error('❌ Invalid JSON format - does not start with { or end with }');
              console.error('❌ Message:', trimmedMessage);
              continue;
            }

            const data = JSON.parse(trimmedMessage);
            handleWebSocketMessage(data);
          } catch (parseError) {
            // Circuit breaker: track JSON errors
            const now = Date.now();
            if (now - lastJsonErrorTimeRef.current > 60000) {
              // Reset error count every minute
              jsonErrorCountRef.current = 0;
            }
            jsonErrorCountRef.current++;
            lastJsonErrorTimeRef.current = now;

            // If too many errors, temporarily disconnect
            if (jsonErrorCountRef.current > 10) {
              console.error('❌ Too many JSON errors, temporarily disconnecting WebSocket');
              if (wsRef.current) {
                wsRef.current.close();
              }
              return;
            }

            console.error('❌ JSON parse error for message:', messageStr);
            console.error('❌ Parse error details:', parseError);
            console.error('❌ Message length:', messageStr.length);
            console.error('❌ Error count:', jsonErrorCountRef.current);
            console.error('❌ Character codes around position 132:',
              messageStr.split('').slice(130, 140).map((char, i) => `${130 + i}: '${char}' (${char.charCodeAt(0)})`));
            console.error('❌ First 200 chars:', messageStr.substring(0, 200));
            console.error('❌ Last 200 chars:', messageStr.substring(Math.max(0, messageStr.length - 200)));

            // Try to find where the JSON might be corrupted
            let braceCount = 0;
            let validJsonEnd = -1;
            for (let i = 0; i < messageStr.length; i++) {
              if (messageStr[i] === '{') braceCount++;
              if (messageStr[i] === '}') {
                braceCount--;
                if (braceCount === 0) {
                  validJsonEnd = i;
                  break;
                }
              }
            }

            if (validJsonEnd > 0) {
              console.error('❌ Possible valid JSON ends at position:', validJsonEnd);
              console.error('❌ Content after valid JSON:', messageStr.substring(validJsonEnd + 1));

              // Try to parse just the valid part
              try {
                const validPart = messageStr.substring(0, validJsonEnd + 1);
                const data = JSON.parse(validPart);
                console.log('✅ Successfully parsed partial JSON');
                handleWebSocketMessage(data);
              } catch (partialError) {
                console.error('❌ Even partial JSON parsing failed:', partialError);
              }
            }
          }
        }
      } catch (error) {
        console.error('❌ WebSocket message handling error:', error);
        console.error('❌ Raw event data:', event.data);
      }
    };

    ws.onclose = (event) => {
      console.log('🔌 WebSocket closed:', event.code, event.reason);
      setConnectionStatus('disconnected');
      setIsConnected(false);
      setRealTimeStatus('disconnected');
      isSubscribedRef.current = false;

      // Auto-reconnect after a delay (unless it was a clean close)
      if (event.code !== 1000 && mountedRef.current) {
        console.log('🔄 Scheduling reconnection in 5 seconds...');
        setTimeout(() => {
          if (mountedRef.current && (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED)) {
            console.log('🔄 Attempting to reconnect...');
            connectWebSocket();
          }
        }, 5000);
      }
    };

    ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      setConnectionStatus('error');
      setRealTimeStatus('error');
      setWsError('Connection error - will retry');
    };
  }, [websocketUrl]);

  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((data) => {
    // Safety check for data structure
    if (!data || typeof data !== 'object') {
      console.warn('⚠️ Invalid message data:', data);
      return;
    }

    console.log('📨 Frontend received WebSocket message:', data.type, `(timeframe: ${chartTimeframe})`, data);

    switch (data.type) {
      case 'connection':
        console.log('✅ Frontend WebSocket connection confirmed');
        break;

      case 'subscription_confirmed':
        console.log(`✅ Frontend subscription confirmed for ${data.forex_pair} ${data.timeframe}`);
        setRealTimeStatus('subscribed');
        break;

      case 'candle_update':
        console.log(`📊 Frontend received candle_update:`, {
          symbol: data.data?.symbol,
          timeframe: data.data?.timeframe,
          close: data.data?.close,
          time: data.data?.time,
          chartTimeframe: chartTimeframe
        });
        handleCandleUpdate(data.data);
        break;

      case 'tick':
        // Handle real-time tick data (throttled for performance)
        // Only process tick updates for non-1m timeframes
        if (chartTimeframe !== '1m') {
          handleTickUpdate(data);
        } else {
          console.log(`📊 Ignoring tick data for 1m timeframe (waiting for candle_update)`);
        }
        break;

      case 'pong':
        // Pong received - no logging needed
        break;

      case 'error':
        console.error('❌ WebSocket error:', data.message);
        setWsError(data.message);
        break;

      default:
        console.warn('📨 Unknown message type:', data.type);
    }
  }, []);

  // Subscribe to forex pair
  const subscribe = useCallback((forexPair, timeframe) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      // For 1m timeframes, act like a trade-bot to get candle data instead of tick data
      const clientType = timeframe === '1m' ? 'trade_bot' : 'frontend';

      const message = {
        type: 'subscribe',
        forex_pair: forexPair,
        timeframe: timeframe,
        client_type: clientType  // Use trade_bot for 1m to get candle updates
      };

      console.log(`📡 Frontend using client_type: ${clientType} for timeframe: ${timeframe}`);
      console.log(`📡 Frontend sending subscription message:`, message);
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn(`⚠️ Frontend WebSocket not ready for subscription. State: ${wsRef.current?.readyState}`);
    }
  }, []);

  // Unsubscribe from forex pair
  const unsubscribe = useCallback((forexPair, timeframe) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'unsubscribe',
        forex_pair: forexPair,
        timeframe: timeframe
      };
      wsRef.current.send(JSON.stringify(message));
    }
  }, []);

  // Handle real-time tick updates (like our working test)
  const handleTickUpdate = useCallback((tickData) => {
    // Mark as real-time update
    setIsRealTimeUpdate(true);

    setCandleData(prevData => {
      if (prevData.length === 0) return prevData;

      const updatedData = [...prevData];
      const lastIndex = updatedData.length - 1;
      const currentCandle = updatedData[lastIndex];

      // Calculate what the current candle timeframe should be based on current time
      const now = Math.floor(Date.now() / 1000); // Current time in seconds
      const timeframeSeconds = getTimeframeInMs(chartTimeframe) / 1000;

      // Calculate the start time of the current candle period
      // For proper candle alignment, we need to align to timeframe boundaries
      let currentCandleStart;

      if (chartTimeframe === '5m') {
        // For 5-minute candles, align to 5-minute boundaries (00, 05, 10, 15, etc.)
        const minutes = Math.floor(now / 60);
        const alignedMinutes = Math.floor(minutes / 5) * 5;
        currentCandleStart = alignedMinutes * 60;
      } else if (chartTimeframe === '1m') {
        // For 1-minute candles, align to minute boundaries
        currentCandleStart = Math.floor(now / 60) * 60;
      } else if (chartTimeframe === '1h') {
        // For 1-hour candles, align to hour boundaries
        currentCandleStart = Math.floor(now / 3600) * 3600;
      } else {
        // Generic alignment for other timeframes
        currentCandleStart = Math.floor(now / timeframeSeconds) * timeframeSeconds;
      }

      // Check if we need to create a new candle or update the existing one
      if (currentCandle.time < currentCandleStart) {
        // The last candle is from a previous timeframe period, create a new candle
        // IMPORTANT: New candle should open at the close price of the previous candle
        const newCandle = {
          time: currentCandleStart,
          open: currentCandle.close, // Open at previous candle's close price
          high: Math.max(currentCandle.close, tickData.price),
          low: Math.min(currentCandle.close, tickData.price),
          close: tickData.price,
          volume: 0
        };
        updatedData.push(newCandle);
      } else if (currentCandle.time === currentCandleStart) {
        // Update the existing current candle (we're in the same timeframe period)
        const updatedCandle = {
          ...currentCandle,
          close: tickData.price,
          high: Math.max(currentCandle.high, tickData.price),
          low: Math.min(currentCandle.low, tickData.price)
        };
        updatedData[lastIndex] = updatedCandle;
      } else {
        // This shouldn't happen, but log it for debugging
        console.warn(`⚠️ Unexpected candle timing: currentCandle.time=${currentCandle.time}, currentCandleStart=${currentCandleStart}`);
        // Still update the candle as fallback
        const updatedCandle = {
          ...currentCandle,
          close: tickData.price,
          high: Math.max(currentCandle.high, tickData.price),
          low: Math.min(currentCandle.low, tickData.price)
        };
        updatedData[lastIndex] = updatedCandle;
      }

      return updatedData;
    });

    setLastUpdate(new Date());
    setUpdateCount(prev => prev + 1);
    lastCandleRef.current = { ...lastCandleRef.current, close: tickData.price };

    // Reset real-time update flag after a longer delay to ensure chart updates complete
    setTimeout(() => setIsRealTimeUpdate(false), 2000);
  }, [chartTimeframe]);

  // Validate candle sequence
  const validateCandleSequence = useCallback((newCandle, existingData) => {
    if (existingData.length === 0) return true;

    const lastCandle = existingData[existingData.length - 1];
    const timeframeMs = getTimeframeInMs(chartTimeframe);
    const expectedNextTime = lastCandle.time + timeframeMs;

    // Allow some tolerance for timing differences (±30 seconds)
    const tolerance = 30 * 1000;
    const timeDiff = Math.abs(newCandle.time - expectedNextTime);

    if (timeDiff > tolerance && newCandle.time > lastCandle.time + timeframeMs) {
      console.warn(`⚠️ Candle sequence gap detected: expected ~${expectedNextTime}, got ${newCandle.time}`);
      return false;
    }

    return true;
  }, [chartTimeframe]);

  // Update session and memory storage
  const updateStorageWithNewCandle = useCallback((updatedData) => {
    if (!instrument || !chartTimeframe) return;

    try {
      const forexPair = instrument.replace("/", "");
      const cacheKey = `${forexPair}_${chartTimeframe}`;
      const cacheData = {
        candles: updatedData,
        timestamp: Date.now(),
        source: 'websocket_update'
      };

      // Update session storage
      sessionStorage.setItem(`candles_${cacheKey}`, JSON.stringify(cacheData));

      // Update global cache if it exists
      if (window.globalCandleCache) {
        window.globalCandleCache.set(cacheKey, cacheData);
      }
    } catch (error) {
      console.warn('⚠️ Failed to update storage:', error);
    }
  }, [instrument, chartTimeframe]);

  // Handle real-time candle updates
  const handleCandleUpdate = useCallback((newCandle) => {
    // Mark as real-time update
    console.log('🔄 Setting isRealTimeUpdate = true for candle update');
    setIsRealTimeUpdate(true);

    setCandleData(prevData => {
      // Validate candle sequence
      if (!validateCandleSequence(newCandle, prevData)) {
        console.warn('⚠️ Invalid candle sequence, skipping update');
        return prevData;
      }

      const updatedData = [...prevData];
      const lastIndex = updatedData.length - 1;

      // Check if this is an update to the current candle or a new candle
      if (lastIndex >= 0) {
        const lastCandle = updatedData[lastIndex];

        // Use exact time comparison for candle updates (more precise than tick updates)
        if (newCandle.time === lastCandle.time) {
          // Update existing candle (same timeframe period)
          updatedData[lastIndex] = {
            ...lastCandle,
            high: Math.max(lastCandle.high, newCandle.high),
            low: Math.min(lastCandle.low, newCandle.low),
            close: newCandle.close,
            volume: newCandle.volume,
            time: newCandle.time
          };
        } else if (newCandle.time > lastCandle.time) {
          // Add new candle (new timeframe period)
          updatedData.push(newCandle);
        } else {
          // This shouldn't happen with proper data - reject out-of-order candles
          console.warn(`⚠️ Received candle with older timestamp: ${new Date(newCandle.time * 1000).toISOString()} vs ${new Date(lastCandle.time * 1000).toISOString()}`);
          console.warn(`⚠️ Rejecting out-of-order candle to maintain time sequence`);
          // Don't add the candle - return the original data to maintain time ordering
          return prevData;
        }
      } else {
        // First candle
        updatedData.push(newCandle);
      }

      // Sort data by time to ensure ascending order (safety net)
      const sortedData = updatedData.sort((a, b) => a.time - b.time);

      // Update storage with new data
      updateStorageWithNewCandle(sortedData);

      return sortedData;
    });

    setLastUpdate(new Date());
    setUpdateCount(prev => prev + 1);
    lastCandleRef.current = newCandle;

    // Clear any existing timeout and set a new one
    if (realTimeUpdateTimeoutRef.current) {
      clearTimeout(realTimeUpdateTimeoutRef.current);
    }

    // Reset real-time update flag after a longer delay to ensure chart updates complete
    realTimeUpdateTimeoutRef.current = setTimeout(() => {
      console.log('🔄 Resetting isRealTimeUpdate = false after candle update');
      setIsRealTimeUpdate(false);
      realTimeUpdateTimeoutRef.current = null;
    }, 2000);
  }, [chartTimeframe, validateCandleSequence, updateStorageWithNewCandle]);

  // Get timeframe in milliseconds
  const getTimeframeInMs = (timeframe) => {
    const timeframeMap = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000
    };
    return timeframeMap[timeframe] || 60 * 1000; // Default to 1 minute
  };

  // Connect to WebSocket when component mounts (only once)
  useEffect(() => {
    mountedRef.current = true;

    if (enableRealTime) {
      // Delay connection to avoid rapid reconnections
      const timer = setTimeout(() => {
        if (mountedRef.current && !wsRef.current) {
          connectWebSocket();
        }
      }, 1000);

      // Only cleanup timer, keep WebSocket connection alive
      return () => {
        mountedRef.current = false;
        clearTimeout(timer);
        // Don't close WebSocket connection here - keep it persistent
      };
    }
  }, [enableRealTime, connectWebSocket]); // Only depend on enableRealTime and connectWebSocket

  // Subscribe to real-time data when connected (only once per instrument/timeframe combination)
  useEffect(() => {
    if (isConnected && enableRealTime && instrument && chartTimeframe) {
      // Convert instrument format (EUR/USD -> EURUSD)
      const forexPair = instrument.replace("/", "");
      const subscriptionKey = `${forexPair}_${chartTimeframe}`;

      // Only subscribe if we haven't already subscribed to this exact combination
      if (!isSubscribedRef.current || subscriptionKeyRef.current !== subscriptionKey) {
        // Unsubscribe from previous subscription if it exists
        if (isSubscribedRef.current && subscriptionKeyRef.current) {
          const [prevPair, prevTimeframe] = subscriptionKeyRef.current.split('_');
          unsubscribe(prevPair, prevTimeframe);
        }

        // Subscribe to WebSocket
        console.log(`📡 Frontend subscribing to: ${forexPair}_${chartTimeframe}`);
        subscribe(forexPair, chartTimeframe);
        isSubscribedRef.current = true;
        subscriptionKeyRef.current = subscriptionKey;
        setRealTimeStatus('connecting');
      }
    }

    // Only cleanup when component is actually being destroyed, not just hidden
    return () => {
      // Don't unsubscribe on tab changes or component re-renders
      // Only unsubscribe when the instrument/timeframe actually changes
    };
  }, [isConnected, enableRealTime, instrument, chartTimeframe, subscribe, unsubscribe]);

  // Handle page unload/navigation - only then do we cleanup WebSocket
  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log('🔌 Page unloading - cleaning up WebSocket connection');
      if (isSubscribedRef.current && subscriptionKeyRef.current) {
        const [forexPair, timeframe] = subscriptionKeyRef.current.split('_');
        unsubscribe(forexPair, timeframe);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('📱 Tab became visible - ensuring WebSocket connection');
        // Reconnect if needed when tab becomes visible again
        if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
          connectWebSocket();
        }
      }
      // Don't disconnect when tab becomes hidden - keep connection alive
    };

    // Listen for page unload and visibility changes
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [connectWebSocket, unsubscribe]);

  // Fix candle continuity gaps in historical data
  const fixCandleContinuity = useCallback((candles) => {
    if (!candles || candles.length <= 1) return candles;

    const fixedCandles = [candles[0]]; // Keep first candle as-is

    for (let i = 1; i < candles.length; i++) {
      const prevCandle = fixedCandles[i - 1];
      const currentCandle = candles[i];

      // Check if there's a gap between candles
      if (currentCandle.open !== prevCandle.close) {
        // console.log(`🔧 Fixing candle continuity gap: candle ${i} open=${currentCandle.open} should be ${prevCandle.close}`);
        // Fix the current candle's open price to match previous candle's close
        const fixedCandle = {
          ...currentCandle,
          open: prevCandle.close,
          // Adjust high and low if necessary to accommodate the new open price
          high: Math.max(currentCandle.high, prevCandle.close),
          low: Math.min(currentCandle.low, prevCandle.close)
        };

        fixedCandles.push(fixedCandle);
      } else {
        fixedCandles.push(currentCandle);
      }
    }

    return fixedCandles;
  }, []);

  // Update candle data when initial data changes
  useEffect(() => {
    if (initialCandleData && initialCandleData.length > 0) {
      // Mark as NOT a real-time update for initial data
      setIsRealTimeUpdate(false);

      // Sort initial data by time to ensure proper ordering
      const sortedData = [...initialCandleData].sort((a, b) => a.time - b.time);

      // Fix candle continuity gaps in historical data
      const fixedCandleData = fixCandleContinuity(sortedData);

      setCandleData(fixedCandleData);

      // Update storage with fixed data
      updateStorageWithNewCandle(fixedCandleData);
    }
  }, [initialCandleData, updateStorageWithNewCandle, fixCandleContinuity]);

  // Debug: Log candle data changes
  useEffect(() => {
    console.log(`📊 Candle data updated: ${candleData.length} candles`);
  }, [candleData]);

  // Real-time status indicator component
  const RealTimeStatusIndicator = () => {
    const getStatusColor = () => {
      switch (realTimeStatus) {
        case 'connected':
        case 'subscribed':
          return '#26a69a'; // Green
        case 'connecting':
        case 'reconnecting':
          return '#ff9800'; // Orange
        case 'error':
        case 'failed':
          return '#ef5350'; // Red
        default:
          return '#757575'; // Gray
      }
    };

    const getStatusText = () => {
      switch (realTimeStatus) {
        case 'connected':
          return 'Connected';
        case 'subscribed':
          return 'Live';
        case 'connecting':
          return 'Connecting...';
        case 'reconnecting':
          return 'Reconnecting...';
        case 'error':
          return 'Error';
        case 'failed':
          return 'Failed';
        default:
          return 'Offline';
      }
    };

    if (!enableRealTime) {
      return null;
    }

    return (
      <div className="absolute top-4 right-4 z-10 flex items-center space-x-2 bg-black bg-opacity-50 rounded-lg px-3 py-2">
        <div
          className="w-2 h-2 rounded-full"
          style={{ backgroundColor: getStatusColor() }}
        />
        <span className="text-white text-xs font-medium">
          {getStatusText()}
        </span>
        {realTimeStatus === 'subscribed' && updateCount > 0 && (
          <span className="text-gray-300 text-xs">
            ({updateCount} updates)
          </span>
        )}
      </div>
    );
  };

  // Real-time data info component
  const RealTimeDataInfo = () => {
    if (!enableRealTime || !lastUpdate) {
      return null;
    }

    return (
      <div className="absolute bottom-4 right-4 z-10 bg-black bg-opacity-50 rounded-lg px-3 py-2">
        <div className="text-white text-xs">
          <div>Last Update: {lastUpdate.toLocaleTimeString()}</div>
          {lastCandleRef.current && (
            <div className="mt-1">
              {instrument}: {lastCandleRef.current.close.toFixed(5)}
              <span className={`ml-2 ${
                lastCandleRef.current.close >= lastCandleRef.current.open
                  ? 'text-green-400'
                  : 'text-red-400'
              }`}>
                {lastCandleRef.current.close >= lastCandleRef.current.open ? '📈' : '📉'}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full h-full">
      {/* Real-time status indicator */}
      <RealTimeStatusIndicator />

      {/* Real-time data info */}
      <RealTimeDataInfo />

      {/* Error indicator */}
      {wsError && (
        <div className="absolute top-16 right-4 z-10 bg-red-600 bg-opacity-90 rounded-lg px-3 py-2">
          <span className="text-white text-xs">
            ⚠️ {wsError}
          </span>
        </div>
      )}

      {/* Original OptimizedTradingChart with real-time data */}
      <OptimizedTradingChart
        candleData={candleData}
        indicators={indicators}
        trades={trades}
        timeframe={displayTimeframe}
        chartTimeframe={chartTimeframe}
        instrument={instrument}
        marketStatus={marketStatus}
        strategyInfo={strategyInfo}
        strategy={strategy}
        isRealTimeUpdate={isRealTimeUpdate}
        {...otherProps}
      />
    </div>
  );
};

export default RealTimeOptimizedTradingChart;
