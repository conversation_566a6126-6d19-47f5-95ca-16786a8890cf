import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { useRouter } from 'next/router';
import axios from 'axios';
import { USE_FIREBASE_EMULATOR } from '../config';
import { auth } from '../../firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';

const DELETE_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/delete_strategy"
  : "https://delete-strategy-ihjc6tjxia-uc.a.run.app";

export default function StrategyCard({
  strategy,
  onSelect,
  onDelete,
  onDeploy,
  onAddToMyStrategies,
  isDeployed,
  isCommunityStrategy = false,
  isAddingToCollection = false,
  isAlreadyAdded = false,
  hasPrivileges = false,
  isDeletingCommunityStrategy = false
}) {
  const [isHovered, setIsHovered] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
    });

    return () => unsubscribe();
  }, []);

  const handleEdit = (e) => {
    e.stopPropagation(); // Prevent card click event
    setIsMenuOpen(false);
    router.push({
      pathname: '/strategy-generation',
      query: { strategyId: strategy.id }
    });
  };

  const handleBacktest = (e) => {
    e.stopPropagation(); // Prevent card click event
    setIsMenuOpen(false);
    if (onSelect) {
      onSelect(strategy);
    }
  };

  const handleDeploy = (e) => {
    e.stopPropagation(); // Prevent card click event
    setIsMenuOpen(false);
    if (onDeploy) {
      onDeploy(strategy);
    }
  };

  const toggleMenu = (e) => {
    e.stopPropagation(); // Prevent card click event
    setIsMenuOpen(!isMenuOpen);
  };

  const handleDelete = async () => {
    if (!onDelete) return;

    try {
      await onDelete(strategy.id);
    } catch (error) {
      console.error("Error deleting strategy:", error);
    }
  };

  const handleAddToMyStrategies = (e) => {
    e.stopPropagation(); // Prevent card click event
    setIsMenuOpen(false);
    if (onAddToMyStrategies) {
      onAddToMyStrategies(strategy);
    }
  };

  // If the card is not visible, don't render it
  if (!isVisible) {
    return null;
  }

  // Parse the strategy_json string into an object with error handling
  const strategyData = (() => {
    try {
      if (typeof strategy.strategy_json === 'string') {
        return JSON.parse(strategy.strategy_json);
      }
      return strategy.strategy_json || {};
    } catch (error) {
      console.error('Error parsing strategy JSON:', error);
      console.error('Raw strategy_json:', strategy.strategy_json);
      // Return a fallback object with basic info
      return {
        name: strategy.name || 'Invalid Strategy',
        description: 'Error parsing strategy data',
        instruments: 'Unknown',
        timeframe: 'Unknown',
        tradingSession: [],
        indicators: [],
        entryRules: [],
        exitRules: [],
        riskManagement: {}
      };
    }
  })();

  return (
    <div className="relative group">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ scale: 1.02 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className="relative bg-[#1a1a1a] rounded-xl border border-[#2a2a2a] overflow-hidden cursor-pointer transition-all duration-200 hover:border-[#EFBD3A]/50"
        onClick={toggleMenu}
      >
        {/* Card Header */}
        <div className="p-5 border-b border-[#2a2a2a]">
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-lg font-medium text-[#FEFEFF] truncate">
                  {strategyData.name || 'Unnamed Strategy'}
                </h3>
                {isCommunityStrategy && (
                  <span className="text-xs bg-gradient-to-r from-[#EFBD3A] to-[#FEFEFF] text-black px-2 py-1 rounded-full font-medium">
                    Community
                  </span>
                )}
                {isCommunityStrategy && isAlreadyAdded && (
                  <span className="text-xs bg-gradient-to-r from-green-500 to-green-600 text-white px-2 py-1 rounded-full font-medium">
                    Added
                  </span>
                )}
                {isCommunityStrategy && hasPrivileges && (
                  <span className="text-xs bg-gradient-to-r from-purple-500 to-purple-600 text-white px-2 py-1 rounded-full font-medium">
                    Admin
                  </span>
                )}
              </div>
            </div>
            <span className="text-xs text-[#FEFEFF]/60 bg-[#2a2a2a] px-2 py-1 rounded-full ml-2 flex-shrink-0">
              {(() => {
                try {
                  if (strategy.created_at) {
                    const date = new Date(strategy.created_at);
                    if (isNaN(date.getTime())) {
                      return 'Invalid date';
                    }
                    return format(date, "MMM d, yyyy");
                  }
                  return 'No date';
                } catch (error) {
                  console.error('Error formatting date:', error);
                  return 'Invalid date';
                }
              })()}
            </span>
          </div>
          <div className="mt-2 flex items-center space-x-2">
            <span className="text-sm text-[#EFBD3A]">{strategyData.instruments || 'Unknown Pair'}</span>
            <span className="text-[#FEFEFF]/40">•</span>
            <span className="text-sm text-[#FEFEFF]/80">{strategyData.timeframe || 'Unknown'}</span>
          </div>
          {strategyData.description && (
            <p className="mt-2 text-sm text-[#FEFEFF]/60 line-clamp-2">
              {strategyData.description}
            </p>
          )}
        </div>

        {/* Card Content */}
        <div className="p-5 space-y-4">
          {/* Trading Session */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-[#FEFEFF]/60">Trading Session</h4>
            <div className="flex flex-wrap gap-2">
              {Array.isArray(strategyData.tradingSession) ? (
                strategyData.tradingSession.map((session, index) => (
                  <span 
                    key={index}
                    className="text-xs bg-[#2a2a2a] text-[#FEFEFF]/80 px-2 py-1 rounded-full"
                  >
                    {session}
                  </span>
                ))
              ) : (
                <span 
                  className="text-xs bg-[#2a2a2a] text-[#FEFEFF]/80 px-2 py-1 rounded-full"
                >
                  {strategyData.tradingSession || 'All Sessions'}
                </span>
              )}
            </div>
          </div>

          {/* Risk Management Section */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-[#FEFEFF]/60">Risk Management</h4>
            <div className="flex space-x-4">
              <div className="flex-1 bg-[#2a2a2a] rounded-lg p-3">
                <div className="text-xs text-[#FEFEFF]/60 mb-1">Risk Settings</div>
                <div className="text-sm text-[#FEFEFF]">
                  {strategyData.riskManagement?.riskPercentage || '0'}% Risk
                </div>
                <div className="text-xs text-[#FEFEFF]/60 mt-1">
                  {strategyData.riskManagement?.riskRewardRatio || '0'}:1 RRR
                </div>
              </div>
              <div className="flex-1 bg-[#2a2a2a] rounded-lg p-3">
                <div className="text-xs text-[#FEFEFF]/60 mb-1">Stop Loss Method</div>
                <div className="text-sm text-[#FEFEFF]">
                  {(() => {
                    const rm = strategyData.riskManagement || {};
                    switch (rm.stopLossMethod) {
                      case 'fixed':
                        return `Fixed (${rm.fixedPips || 0} pips)`;
                      case 'indicator':
                        return `${rm.indicatorBasedSL?.indicator || 'Indicator'} Based`;
                      case 'risk':
                        return `Risk Based (${rm.lotSize || 0} lots)`;
                      default:
                        return 'Not Set';
                    }
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* Indicators Section */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-[#FEFEFF]/60">Indicators</h4>
            <div className="flex flex-wrap gap-2">
              {Array.isArray(strategyData.indicators) && strategyData.indicators.length > 0 ? (
                strategyData.indicators.map((ind, index) => (
                  <span 
                    key={index}
                    className="text-xs bg-[#2a2a2a] text-[#FEFEFF]/80 px-2 py-1 rounded-full"
                  >
                    {ind.type || ind.indicator_class}
                    {ind.parameters && Object.keys(ind.parameters).length > 0 && (
                      <span className="text-[#FEFEFF]/40">
                        {" "}({Object.entries(ind.parameters)
                          .map(([k, v]) => `${k}: ${v}`)
                          .join(", ")})
                      </span>
                    )}
                  </span>
                ))
              ) : (
                <span className="text-sm text-[#FEFEFF]/40">No indicators</span>
              )}
            </div>
          </div>

          {/* Rules Summary */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-[#FEFEFF]/60">Rules Summary</h4>
            <div className="flex space-x-4">
              <div className="flex-1 bg-[#2a2a2a] rounded-lg p-3">
                <div className="text-xs text-[#FEFEFF]/60 mb-1">Entry Rules</div>
                <div className="text-sm text-[#FEFEFF]">
                  {(strategyData.entryRules || []).length} conditions
                </div>
              </div>
              <div className="flex-1 bg-[#2a2a2a] rounded-lg p-3">
                <div className="text-xs text-[#FEFEFF]/60 mb-1">Exit Rules</div>
                <div className="text-sm text-[#FEFEFF]">
                  {(strategyData.exitRules || []).length} conditions
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Hover Effect Line */}
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-[#EFBD3A] to-[#FEFEFF]"
          initial={{ scaleX: 0 }}
          animate={{ scaleX: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>

      {/* Hint Text */}
      <div className="absolute -bottom-6 left-0 right-0 text-center">
        <span className="text-xs text-[#FEFEFF]/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          Click to view actions
        </span>
      </div>

      {/* Action Menu Modal */}
      {isMenuOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black/50 z-40"
            onClick={toggleMenu}
          />
          
          {/* Menu */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#2a2a2a] rounded-xl shadow-lg overflow-hidden z-50 w-64">
            <div className="p-4 border-b border-[#3a3a3a]">
              <h4 className="text-sm font-medium text-[#FEFEFF]">
                {isCommunityStrategy ? "Community Strategy" : "Strategy Actions"}
              </h4>
            </div>
            <div className="p-2">
              {isCommunityStrategy ? (
                // Community strategy actions
                <>
                  <button
                    onClick={isAlreadyAdded ? null : handleAddToMyStrategies}
                    className={`w-full text-left px-4 py-3 text-sm rounded-lg transition-colors flex items-center space-x-2 ${
                      isAlreadyAdded
                        ? 'text-green-400 bg-green-500/20 cursor-not-allowed'
                        : isAddingToCollection
                          ? 'text-[#FEFEFF]/60 cursor-not-allowed'
                          : 'text-[#FEFEFF] hover:bg-[#3a3a3a]'
                    }`}
                    disabled={isAddingToCollection || isAlreadyAdded}
                  >
                    {isAlreadyAdded ? (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>Already in Your Strategies</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <span>{isAddingToCollection ? "Adding..." : "Add to My Strategies"}</span>
                      </>
                    )}
                  </button>

                  {/* Delete button - only show for privileged users */}
                  {hasPrivileges && onDelete && (
                    <button
                      onClick={() => onDelete(strategy.id)}
                      className="w-full text-left px-4 py-3 text-sm text-red-400 hover:bg-red-500/20 rounded-lg transition-colors flex items-center space-x-2"
                      disabled={isDeletingCommunityStrategy}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      <span>{isDeletingCommunityStrategy ? "Deleting..." : "Delete from Community"}</span>
                    </button>
                  )}
                </>
              ) : (
                // Regular strategy actions
                <>
                  <button
                    onClick={handleEdit}
                    className="w-full text-left px-4 py-3 text-sm text-[#FEFEFF] hover:bg-[#3a3a3a] rounded-lg transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span>Edit Strategy</span>
                  </button>
                  <button
                    onClick={handleBacktest}
                    className="w-full text-left px-4 py-3 text-sm text-[#FEFEFF] hover:bg-[#3a3a3a] rounded-lg transition-colors flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span>Run Backtest</span>
                  </button>
                  {/* Deploy button - only show if not already deployed */}
                  {!isDeployed && (
                      <button
                          onClick={handleDeploy}
                          className="w-full text-left px-4 py-3 text-sm text-[#FEFEFF] hover:bg-[#3a3a3a] rounded-lg transition-colors flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span>Deploy Bot</span>
                      </button>
                  )}
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    className="w-full text-left px-4 py-3 text-sm text-red-400 hover:bg-red-500/20 rounded-lg transition-colors flex items-center space-x-2"
                    disabled={isDeleting}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z" />
                    </svg>
                    <span>{isDeleting ? "Deleting..." : "Delete"}</span>
                  </button>
                </>
              )}
            </div>
          </div>
        </>
      )}

      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1a1a1a] p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-[#FEFEFF] mb-4">Confirm Delete</h3>
            <p className="text-[#FEFEFF]/80 mb-6">
              Are you sure you want to delete this strategy? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 text-sm bg-white/10 hover:bg-white/20 text-[#FEFEFF] rounded-lg transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 text-sm bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-lg transition-colors duration-200"
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 