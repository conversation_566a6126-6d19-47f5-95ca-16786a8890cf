import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from "framer-motion";
import DashboardLayout from "../components/DashboardLayout";
import StrategyCard from "../components/StrategyCard";
import { auth } from "../../firebaseConfig";
import { onAuthStateChanged } from "firebase/auth";
import { USE_FIREBASE_EMULATOR } from "../config";
import axios from 'axios';
import { useRouter } from 'next/router';
import dynamic from "next/dynamic";

const RiskManagementModal = dynamic(() => import("../components/RiskManagementModal"), {
  ssr: false,
});

const GET_STRATEGIES_URL = USE_FIREBASE_EMULATOR 
  ? "http://127.0.0.1:5001/oryntrade/us-central1/get_strategies"
  : "https://get-strategies-ihjc6tjxia-uc.a.run.app";

const DELETE_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/delete_strategy"
  : "https://delete-strategy-ihjc6tjxia-uc.a.run.app";

const PUBLISH_STRATEGY_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/publish_strategy"
    : "https://publish-strategy-ihjc6tjxia-uc.a.run.app";

const CONTROL_STRATEGY_URL = USE_FIREBASE_EMULATOR
    ? "http://localhost:8080"
    : "https://control-strategy-ihjc6tjxia-uc.a.run.app";

const GET_COMMUNITY_STRATEGIES_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/get_community_strategies"
    : "https://get-community-strategies-ihjc6tjxia-uc.a.run.app";

const ADD_STRATEGY_TO_USER_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/add_strategy_to_user"
    : "https://add-strategy-to-user-ihjc6tjxia-uc.a.run.app";

const DELETE_COMMUNITY_STRATEGY_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/delete_community_strategy"
    : "https://delete-community-strategy-ihjc6tjxia-uc.a.run.app";

const CHECK_USER_PRIVILEGES_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/check_user_privileges"
    : "https://check-user-privileges-ihjc6tjxia-uc.a.run.app";

export default function StrategyLibrary() {
  const [activeTab, setActiveTab] = useState('strategies');
  const [strategies, setStrategies] = useState([]);
  const [communityStrategies, setCommunityStrategies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [communityLoading, setCommunityLoading] = useState(false);
  const [deployedStrategyIds, setDeployedStrategyIds] = useState(new Set());
  const [selectedStrategy, setSelectedStrategy] = useState(null);
  const [showRiskModal, setShowRiskModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState(null);
  const [communityError, setCommunityError] = useState(null);
  const [hasPrivileges, setHasPrivileges] = useState(false);
  const [isDeletingCommunityStrategy, setIsDeletingCommunityStrategy] = useState(false);
  const router = useRouter();

  const fetchStrategies = async () => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      setError("No user logged in");
      setLoading(false);
      return;
    }
    
    setLoading(true);
    try {
      const response = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${currentUser.uid}`);
      // Instead of querying Firestore directly, check the is_deployed field in the strategies we already have
      const deployedIds = new Set(
          response.data
              .filter(strategy => strategy.is_deployed === true)
              .map(strategy => strategy.id)
      );
      setDeployedStrategyIds(deployedIds);

      console.log("Deployed strategy IDs:", Array.from(deployedIds));

      // Sort strategies by creation date, newest first
      const sortedStrategies = response.data.sort((a, b) => {
        const dateA = new Date(a.created_at || 0);
        const dateB = new Date(b.created_at || 0);
        return dateB - dateA;
      });

      // Process each strategy to ensure proper data structure
      const processedStrategies = sortedStrategies.map(strategy => {
        let strategyData;
        try {
          strategyData = typeof strategy.strategy_json === 'string' 
            ? JSON.parse(strategy.strategy_json)
            : strategy.strategy_json;

          // Ensure risk management has the correct structure
          if (strategyData.riskManagement) {
            strategyData.riskManagement = {
              riskPercentage: strategyData.riskManagement.riskPercentage || "",
              riskRewardRatio: strategyData.riskManagement.riskRewardRatio || "",
              stopLossMethod: strategyData.riskManagement.stopLossMethod || "fixed",
              fixedPips: strategyData.riskManagement.fixedPips || "",
              indicatorBasedSL: strategyData.riskManagement.indicatorBasedSL || {
                indicator: "",
                parameters: {}
              },
              lotSize: strategyData.riskManagement.lotSize || ""
            };
          }

          // Ensure indicators have the correct structure
          if (strategyData.indicators) {
            strategyData.indicators = strategyData.indicators.map(indicator => ({
              id: indicator.id || generateId(),
              type: indicator.type || indicator.indicator_class,
              indicator_class: indicator.type || indicator.indicator_class,
              parameters: indicator.parameters || {},
              source: indicator.source || "price"
            }));
          }

          return {
            ...strategy,
            strategy_json: strategyData
          };
        } catch (e) {
          console.error("Error processing strategy:", e);
          return strategy;
        }
      });

      setStrategies(processedStrategies);
      setError(null);
    } catch (error) {
      console.error("Error fetching strategies:", error);
      setError(error.message || "Failed to fetch strategies");
    } finally {
      setLoading(false);
    }
  };

  const fetchCommunityStrategies = async () => {
    setCommunityLoading(true);
    try {
      const response = await axios.get(GET_COMMUNITY_STRATEGIES_URL);
      setCommunityStrategies(response.data);
      setCommunityError(null);
    } catch (error) {
      console.error("Error fetching community strategies:", error);
      setCommunityError(error.message || "Failed to fetch community strategies");
    } finally {
      setCommunityLoading(false);
    }
  };

  // Function to check if user has community strategy privileges
  const checkUserPrivileges = async (user) => {
    try {
      if (!user || !user.email) {
        setHasPrivileges(false);
        return;
      }

      const response = await axios.get(`${CHECK_USER_PRIVILEGES_URL}?email=${encodeURIComponent(user.email)}`);
      setHasPrivileges(response.data.hasPrivileges || false);
    } catch (error) {
      console.error("Error checking user privileges:", error);
      setHasPrivileges(false);
    }
  };

  useEffect(() => {
    fetchStrategies();
    fetchCommunityStrategies();

    // Check user privileges
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        checkUserPrivileges(user);
      } else {
        setHasPrivileges(false);
      }
    });

    return () => unsubscribe();
  }, []);

  const handleDeleteStrategy = async (strategyId) => {
    const currentUser = auth.currentUser;
    if (!currentUser) return;
    
    try {
      await axios.delete(DELETE_STRATEGY_URL, {
        data: {
          firebase_uid: currentUser.uid,
          strategy_id: strategyId
        }
      });
      
      // Refresh the strategies list after successful deletion
      await fetchStrategies();
    } catch (error) {
      console.error("Error deleting strategy:", error);
    }
  };

  const handleStrategySelect = (strategy) => {
    // TODO: Implement strategy selection for backtesting
    console.log("Selected strategy:", strategy);
  };

  const handleDeployStrategy = (strategy) => {
    setSelectedStrategy(strategy);
    setShowRiskModal(true);
  };

  // Function to check if a community strategy is already in user's collection
  const isStrategyAlreadyAdded = (communityStrategy) => {
    if (!communityStrategy || !communityStrategy.strategy_json) return false;

    try {
      const communityStrategyData = typeof communityStrategy.strategy_json === 'string'
        ? JSON.parse(communityStrategy.strategy_json)
        : communityStrategy.strategy_json;

      return strategies.some(userStrategy => {
        try {
          const userStrategyData = typeof userStrategy.strategy_json === 'string'
            ? JSON.parse(userStrategy.strategy_json)
            : userStrategy.strategy_json;

          // Check if this strategy was copied from community (has source field)
          // or if the strategy content is identical
          return userStrategy.source === 'community' &&
                 userStrategyData.name === communityStrategyData.name &&
                 userStrategyData.instruments === communityStrategyData.instruments &&
                 userStrategyData.timeframe === communityStrategyData.timeframe;
        } catch (e) {
          return false;
        }
      });
    } catch (e) {
      return false;
    }
  };

  const handleAddCommunityStrategy = async (strategy) => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      setError("No user logged in");
      return;
    }

    // Check if already added
    if (isStrategyAlreadyAdded(strategy)) {
      alert("This strategy is already in your collection!");
      return;
    }

    setActionLoading(true);
    try {
      const response = await axios.post(ADD_STRATEGY_TO_USER_URL, {
        firebase_uid: currentUser.uid,
        strategy: strategy
      });

      if (response.data.success) {
        // Refresh the user's strategies list
        await fetchStrategies();
        // Show success message or toast
        alert("Strategy added to your collection successfully!");
      }
    } catch (error) {
      console.error("Error adding community strategy:", error);
      setError(error.message || "Failed to add strategy to your collection");
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteCommunityStrategy = async (strategyId) => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      setError("No user logged in");
      return;
    }

    if (!hasPrivileges) {
      alert("You don't have permission to delete community strategies.");
      return;
    }

    // Confirm deletion
    if (!confirm("Are you sure you want to delete this community strategy? This action cannot be undone.")) {
      return;
    }

    setIsDeletingCommunityStrategy(true);
    try {
      const response = await axios.delete(DELETE_COMMUNITY_STRATEGY_URL, {
        data: {
          user_email: currentUser.email,
          strategy_id: strategyId
        }
      });

      if (response.data.success) {
        // Refresh the community strategies list
        await fetchCommunityStrategies();
        alert("Community strategy deleted successfully!");
      } else {
        alert("Failed to delete community strategy: " + response.data.message);
      }
    } catch (error) {
      console.error("Error deleting community strategy:", error);
      setError(error.message || "Failed to delete community strategy");
    } finally {
      setIsDeletingCommunityStrategy(false);
    }
  };

  const handleRiskConfirm = async (riskParams) => {
    if (!selectedStrategy || !auth.currentUser) return;

    setActionLoading(true);
    try {
      // Get a fresh token
      const idToken = await auth.currentUser.getIdToken(true);

      // First, publish the strategy with risk parameters
      console.log("Publishing strategy with ID:", selectedStrategy.id);

      // Prepare the strategy JSON with risk parameters
      let strategyJson = selectedStrategy.strategy_json;
      if (typeof strategyJson === 'string') {
        try {
          strategyJson = JSON.parse(strategyJson);
        } catch (e) {
          console.error('Error parsing strategy JSON:', e);
          strategyJson = {};
        }
      }

      // Add risk management parameters to the strategy JSON
      // Make sure we preserve the existing stopLoss and takeProfit if they exist
      if (strategyJson.riskManagement) {
        // Merge the existing risk management with the new parameters
        strategyJson.riskManagement = {
          ...strategyJson.riskManagement,
          ...riskParams
        };
      } else {
        // Just use the new parameters
        strategyJson.riskManagement = riskParams;
      }

      // Ensure stopLoss and takeProfit are always present
      if (!strategyJson.riskManagement.stopLoss) {
        console.warn("stopLoss is missing from risk management, adding default value");
        strategyJson.riskManagement.stopLoss = "1";
        strategyJson.riskManagement.stopLossUnit = "percentage";
      }

      if (!strategyJson.riskManagement.takeProfit) {
        console.warn("takeProfit is missing from risk management, adding default value");
        strategyJson.riskManagement.takeProfit = "2";
        strategyJson.riskManagement.takeProfitUnit = "percentage";
      }

      // Log the final risk management configuration
      console.log("Final risk management configuration:", strategyJson.riskManagement);

      // Convert the strategy JSON to a string
      const strategyJsonString = JSON.stringify(strategyJson);
      console.log("Stringified strategy JSON:", strategyJsonString);

      const publishResponse = await axios.post(
          PUBLISH_STRATEGY_URL,
          {
            strategy_id: selectedStrategy.id,
            user_id: auth.currentUser.uid,
            strategy_json: strategyJsonString,  // Send as a string
            risk_management: riskParams
          },
          {
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
          }
      );

      // Then, start the bot
      await axios.post(
          `${CONTROL_STRATEGY_URL}/control-strategy/${auth.currentUser.uid}/${selectedStrategy.id}`,
          { command: "start" },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${idToken}`,
            },
          }
      );

      // Log the success response
      console.log("Strategy published successfully:", publishResponse.data);

      // Close the modal
      setShowRiskModal(false);

      // Show success message (you can add a toast notification here if you have one)
      console.log("Strategy deployed successfully, redirecting to Trade Bots page...");

      // Navigate to the trade bots page
      window.location.href = "http://localhost:3000/trade-bots";
    } catch (error) {
      console.error("Error deploying strategy:", error);

      // Extract the error message from the response if available
      let errorMessage = "Failed to deploy strategy";

      if (error.response && error.response.data) {
        console.log("Server error response:", error.response.data);

        if (error.response.data.message) {
          errorMessage = `Deployment failed: ${error.response.data.message}`;
          console.log("Error message details:", error.response.data.message);
        }
      } else {
        console.log("No response data available, raw error:", error.message);
      }

      // Log the request payload for debugging
      if (error.config && error.config.data) {
        try {
          const requestData = JSON.parse(error.config.data);
          console.log("Request payload that caused the error:", requestData);
        } catch (e) {
          console.log("Could not parse request data:", error.config.data);
        }
      }

      setError(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen text-white p-6"
      >
        <div className="w-full">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-[#FEFEFF] mb-2">Strategy Library</h1>
            <p className="text-[#FEFEFF]/60">
              View, test, and manage your trading strategies
            </p>
          </div>

          {/* Tabs */}
          <div className="mb-8">
            <div className="border-b border-white/10">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('strategies')}
                  className={`${
                    activeTab === 'strategies'
                      ? 'border-[#EFBD3A] text-[#EFBD3A]'
                      : 'border-transparent text-[#FEFEFF]/60 hover:text-[#FEFEFF] hover:border-[#FEFEFF]/40'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  My Strategies
                </button>
                <button
                  onClick={() => setActiveTab('results')}
                  className={`${
                    activeTab === 'results'
                      ? 'border-[#EFBD3A] text-[#EFBD3A]'
                      : 'border-transparent text-[#FEFEFF]/60 hover:text-[#FEFEFF] hover:border-[#FEFEFF]/40'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Backtest Results
                </button>
                <button
                  onClick={() => setActiveTab('community')}
                  className={`${
                    activeTab === 'community'
                      ? 'border-[#EFBD3A] text-[#EFBD3A]'
                      : 'border-transparent text-[#FEFEFF]/60 hover:text-[#FEFEFF] hover:border-[#FEFEFF]/40'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Community Strategies
                </button>
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="mt-8">
            {activeTab === 'strategies' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FEFEFF]"></div>
                  </div>
                ) : error ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-red-500 mb-2">Error Loading Strategies</h3>
                    <p className="text-[#FEFEFF]/60">{error}</p>
                  </div>
                ) : strategies.length === 0 ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-[#FEFEFF] mb-2">No Strategies Found</h3>
                    <p className="text-[#FEFEFF]/60 mb-4">
                      Create your first strategy in the Strategy Generation page to get started
                    </p>
                    <button
                      onClick={() => router.push('/strategy-generation')}
                      className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-blue-500/30 border border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all duration-200"
                    >
                      Create Strategy
                    </button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {strategies.map((strategy) => (
                      <StrategyCard
                        key={strategy.id}
                        strategy={strategy}
                        onSelect={handleStrategySelect}
                        onDelete={handleDeleteStrategy}
                        onDeploy={handleDeployStrategy}
                        isDeployed={deployedStrategyIds.has(strategy.id)}
                      />
                    ))}
                  </div>
                )}
              </motion.div>
            )}

            {activeTab === 'results' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="p-6 bg-white/5 border border-white/10 rounded-xl"
              >
                <h2 className="text-xl font-semibold text-[#FEFEFF] mb-4">Backtest Results</h2>
                <p className="text-[#FEFEFF]/60">
                  View and analyze the results of your backtests
                </p>
              </motion.div>
            )}

            {activeTab === 'community' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {communityLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FEFEFF]"></div>
                  </div>
                ) : communityError ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-red-500 mb-2">Error Loading Community Strategies</h3>
                    <p className="text-[#FEFEFF]/60">{communityError}</p>
                  </div>
                ) : communityStrategies.length === 0 ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-[#FEFEFF] mb-2">No Community Strategies Available</h3>
                    <p className="text-[#FEFEFF]/60 mb-4">
                      Community strategies will be available soon. Check back later!
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {communityStrategies.map((strategy) => (
                      <StrategyCard
                        key={strategy.id}
                        strategy={strategy}
                        onSelect={null}
                        onDelete={hasPrivileges ? handleDeleteCommunityStrategy : null}
                        onDeploy={null}
                        onAddToMyStrategies={handleAddCommunityStrategy}
                        isDeployed={false}
                        isCommunityStrategy={true}
                        isAddingToCollection={actionLoading}
                        isAlreadyAdded={isStrategyAlreadyAdded(strategy)}
                        hasPrivileges={hasPrivileges}
                        isDeletingCommunityStrategy={isDeletingCommunityStrategy}
                      />
                    ))}
                  </div>
                )}
              </motion.div>
            )}
          </div>
        </div>
      </motion.div>
      {/* Risk Management Modal */}
      <AnimatePresence>
        {showRiskModal && (
            <RiskManagementModal
                isOpen={showRiskModal}
                onClose={() => setShowRiskModal(false)}
                onConfirm={handleRiskConfirm}
                strategy={selectedStrategy}
            />
        )}
      </AnimatePresence>
    </DashboardLayout>
  );
} 