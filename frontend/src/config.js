// src/config.js

// Firebase Configuration
export const USE_FIREBASE_EMULATOR = true;  // Keep Firebase emulator for development

// Environment detection for Firebase services
const isDevelopment = process.env.NODE_ENV === 'development' || USE_FIREBASE_EMULATOR;

// WebSocket Configuration (independent of Firebase emulator)
// This allows us to use production WebSocket while keeping Firebase in emulator mode
export const USE_PRODUCTION_WEBSOCKET = true;  // Use production WebSocket even in development

// WebSocket Configuration with Security
const getWebSocketURL = () => {
  if (!USE_PRODUCTION_WEBSOCKET) {
    return 'ws://localhost:8081/ws';  // Development - local WebSocket service (no auth needed)
  }

  // Production - Cloud Run service with API key authentication
  const baseUrl = 'wss://oryn-websocket-service-ihjc6tjxia-uc.a.run.app/ws';
  // Try to get API key from environment, fallback to hardcoded for testing
  const apiKey = process.env.REACT_APP_WEBSOCKET_API_KEY || 'b24735c7f1ccbff6de39328ebbfdbf4e9e2999699d062119915575184f576a4b';

  console.log('🔑 WebSocket API Key loaded:', apiKey ? 'YES' : 'NO');
  console.log('🔑 API Key source:', process.env.REACT_APP_WEBSOCKET_API_KEY ? 'ENV' : 'HARDCODED');
  console.log('🔑 API Key value:', apiKey ? `${apiKey.substring(0, 10)}...` : 'undefined');
  console.log('🔑 All env vars:', Object.keys(process.env).filter(key => key.startsWith('REACT_APP')));
  console.log('🔗 WebSocket Base URL:', baseUrl);

  if (apiKey) {
    const fullUrl = `${baseUrl}?api_key=${apiKey}`;
    console.log('🌐 Final WebSocket URL:', fullUrl);
    return fullUrl;
  }

  // Fallback to base URL (will rely on origin-based auth)
  console.log('⚠️ No API key found, using base URL');
  return baseUrl;
};

export const WEBSOCKET_URL = getWebSocketURL();

// WebSocket connection options
export const WEBSOCKET_OPTIONS = {
  reconnectInterval: 5000,
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000,
  connectionTimeout: 10000
};
