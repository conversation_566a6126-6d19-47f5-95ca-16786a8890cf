# Oryn Frontend Environment Configuration

# WebSocket Service API Key (for production)
# Get this from your deployment team or generate using scripts/generate_api_keys.sh
REACT_APP_WEBSOCKET_API_KEY=your_frontend_api_key_here

# Firebase Configuration (if needed)
# REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
# REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
# REACT_APP_FIREBASE_PROJECT_ID=your_project_id

# Development Settings
# NODE_ENV=development  # Set automatically by React scripts
