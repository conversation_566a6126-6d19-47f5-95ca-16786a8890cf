"""
Production WebSocket server using FastAPI for Cloud Run deployment.
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from realtime_candle_broadcaster import CandleBroadcaster
from polygon_websocket_service import get_polygon_websocket_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Oryn Real-time WebSocket Service",
    description="Production WebSocket service for real-time forex data streaming",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this for your domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global broadcaster instance
broadcaster = None

# Security configuration
VALID_API_KEYS = set(os.getenv("WEBSOCKET_API_KEYS", "").split(","))
ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "").split(",")

async def authenticate_websocket_connection(websocket: WebSocket, api_key: str = None) -> bool:
    """Authenticate WebSocket connection using API key or origin validation."""

    # Get API key from query parameters if not provided
    if not api_key:
        api_key = websocket.query_params.get("api_key")

    # Get origin from headers
    origin = websocket.headers.get("origin", "")
    user_agent = websocket.headers.get("user-agent", "")

    client_id = f"{websocket.client.host}:{websocket.client.port}"

    # Method 1: API Key authentication
    if api_key and VALID_API_KEYS and api_key in VALID_API_KEYS:
        logger.info(f"✅ API key authentication successful for {client_id}")
        return True

    # Method 2: Origin-based authentication (for development)
    if ALLOWED_ORIGINS:
        # Check if any allowed origin matches the request origin
        for allowed in ALLOWED_ORIGINS:
            if allowed and (allowed in origin or origin.endswith(allowed)):
                logger.info(f"✅ Origin authentication successful for {client_id} (origin: {origin})")
                return True

    # Method 3: Development mode (localhost)
    if os.getenv("ENVIRONMENT", "development") == "development":
        if "localhost" in origin or "127.0.0.1" in websocket.client.host:
            logger.info(f"✅ Development mode authentication for {client_id}")
            return True

    # Method 4: Trade-bot identification (user-agent based)
    if "trade_bot" in user_agent.lower() or "oryn-bot" in user_agent.lower():
        logger.info(f"✅ Trade-bot authentication successful for {client_id}")
        return True

    # Authentication failed
    logger.warning(f"❌ Authentication failed for {client_id} (origin: {origin}, user-agent: {user_agent})")
    await websocket.close(code=1008, reason="Authentication required")
    return False

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    global broadcaster

    try:
        logger.info("🚀 Starting Oryn WebSocket Service")

        # Check required environment variables
        required_vars = ["POLYGON_API_KEY"]
        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            logger.error(f"❌ Missing required environment variables: {missing_vars}")
            raise ValueError(f"Missing environment variables: {missing_vars}")

        # Initialize broadcaster with production state
        use_production_state = os.getenv("ENVIRONMENT", "development") == "production"
        host = "0.0.0.0"  # Bind to all interfaces for Cloud Run
        port = int(os.getenv("PORT", "8080"))  # Cloud Run uses PORT env var

        broadcaster = CandleBroadcaster(host, port, use_production_state)

        # Initialize Polygon client
        polygon_client = get_polygon_websocket_client()
        broadcaster.polygon_client = polygon_client

        # Start Polygon connection
        polygon_client.connect()

        logger.info(f"✅ WebSocket service initialized (production_state: {use_production_state})")

    except Exception as e:
        logger.error(f"❌ Failed to start WebSocket service: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up on shutdown."""
    global broadcaster

    try:
        logger.info("🛑 Shutting down WebSocket service")

        if broadcaster:
            if broadcaster.polygon_client:
                broadcaster.polygon_client.disconnect()

            # Clean up any remaining connections
            if broadcaster.state_manager:
                # In production, we might want to mark this instance as offline
                pass

        logger.info("✅ WebSocket service shutdown complete")

    except Exception as e:
        logger.error(f"❌ Error during shutdown: {e}")

@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "Oryn Real-time WebSocket Service",
        "status": "running",
        "version": "1.0.0",
        "websocket_endpoint": "/ws",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for Cloud Run."""
    global broadcaster

    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "oryn-websocket",
            "version": "1.0.0"
        }

        if broadcaster:
            broadcaster_status = broadcaster.get_status()
            health_status.update({
                "websocket_server": "running" if broadcaster.is_running else "stopped",
                "connected_clients": broadcaster_status.get("connected_clients", 0),
                "polygon_connected": broadcaster_status.get("polygon_status", {}).get("is_connected", False),
                "active_subscriptions": len(broadcaster_status.get("active_subscriptions", {}))
            })
        else:
            health_status["websocket_server"] = "not_initialized"

        return health_status

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.get("/status")
async def get_status():
    """Get detailed service status."""
    global broadcaster

    try:
        if not broadcaster:
            raise HTTPException(status_code=503, detail="WebSocket service not initialized")

        status = broadcaster.get_status()
        status["timestamp"] = datetime.now(timezone.utc).isoformat()

        return status

    except Exception as e:
        logger.error(f"Error getting status: {e}")
        raise HTTPException(status_code=500, detail=f"Status error: {str(e)}")

@app.post("/subscribe")
async def manage_subscription(request: Dict[str, Any]):
    """Manage Polygon subscriptions (for testing/admin purposes)."""
    global broadcaster

    try:
        if not broadcaster or not broadcaster.polygon_client:
            raise HTTPException(status_code=503, detail="WebSocket service not ready")

        forex_pair = request.get("forex_pair")
        timeframe = request.get("timeframe", "1m")
        action = request.get("action", "subscribe")

        if not forex_pair:
            raise HTTPException(status_code=400, detail="Missing forex_pair")

        if action == "subscribe":
            broadcaster.polygon_client.subscribe_to_forex_pair(forex_pair, timeframe)
            message = f"Subscribed to {forex_pair} {timeframe}"
        elif action == "unsubscribe":
            broadcaster.polygon_client.unsubscribe_from_forex_pair(forex_pair)
            message = f"Unsubscribed from {forex_pair}"
        else:
            raise HTTPException(status_code=400, detail="Invalid action")

        return {
            "message": message,
            "forex_pair": forex_pair,
            "timeframe": timeframe,
            "action": action,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error managing subscription: {e}")
        raise HTTPException(status_code=500, detail=f"Subscription error: {str(e)}")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, api_key: str = None):
    """Main WebSocket endpoint for client connections."""
    global broadcaster

    if not broadcaster:
        await websocket.close(code=1011, reason="Service not ready")
        return

    client_id = f"{websocket.client.host}:{websocket.client.port}"
    logger.info(f"WebSocket connection attempt from {client_id}")

    # Authenticate the connection
    if not await authenticate_websocket_connection(websocket, api_key):
        return

    try:
        await websocket.accept()
        logger.info(f"WebSocket connection accepted for {client_id}")

        # Add client to broadcaster
        broadcaster.clients.add(websocket)

        # Generate session info
        import uuid
        session_id = str(uuid.uuid4())
        user_id = f"user_{client_id.replace(':', '_')}"

        # Store client info
        broadcaster.client_info[websocket] = {
            'user_id': user_id,
            'session_id': session_id,
            'client_id': client_id,
            'connected_at': datetime.now(timezone.utc)
        }

        # Send welcome message
        welcome_message = {
            "type": "connection",
            "status": "connected",
            "message": "Connected to Oryn real-time data stream",
            "session_id": session_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        await websocket.send_text(json.dumps(welcome_message))

        # Handle client messages
        while True:
            try:
                message = await websocket.receive_text()
                await broadcaster._handle_client_message(websocket, message)
            except WebSocketDisconnect:
                logger.info(f"Client {client_id} disconnected")
                break
            except Exception as e:
                logger.error(f"Error handling message from {client_id}: {e}")
                break

    except Exception as e:
        logger.error(f"WebSocket error for {client_id}: {e}")

    finally:
        # Clean up client
        try:
            if broadcaster and websocket in broadcaster.clients:
                await broadcaster._cleanup_client(websocket)
            logger.info(f"Cleaned up client {client_id}")
        except Exception as e:
            logger.error(f"Error cleaning up client {client_id}: {e}")

def create_app() -> FastAPI:
    """Create and configure the FastAPI app."""
    return app

if __name__ == "__main__":
    # For local development
    port = int(os.getenv("PORT", "8080"))

    logger.info(f"🚀 Starting Oryn WebSocket Service on port {port}")
    logger.info(f"🌐 WebSocket endpoint: ws://localhost:{port}/ws")
    logger.info(f"📊 Health check: http://localhost:{port}/health")
    logger.info(f"📈 Status: http://localhost:{port}/status")

    uvicorn.run(
        "production_websocket_server:app",
        host="0.0.0.0",
        port=port,
        reload=False,  # Disable reload in production
        log_level="info"
    )
