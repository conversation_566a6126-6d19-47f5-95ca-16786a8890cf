# 🔐 WebSocket Service Security Guide

This guide covers security configurations for the Oryn WebSocket service in production.

## 🚨 Security Layers Implemented

### 1. **API Key Authentication**
- **Purpose**: Restrict access to authorized clients only
- **Implementation**: Query parameter or header-based API keys
- **Usage**: `wss://your-service.com/ws?api_key=your_secure_key`

### 2. **Origin-Based Validation**
- **Purpose**: Allow connections only from trusted domains
- **Implementation**: HTTP Origin header validation
- **Usage**: Automatically validates based on request origin

### 3. **User-Agent Identification**
- **Purpose**: Identify and allow trade-bot connections
- **Implementation**: User-Agent header contains "trade_bot" or "oryn-bot"
- **Usage**: Automatic for trade-bot service

### 4. **Development Mode Bypass**
- **Purpose**: Allow localhost connections during development
- **Implementation**: Checks for localhost/127.0.0.1 origins
- **Usage**: Automatic in development environment

## 🔑 Configuration Setup

### **Step 1: Generate API Keys**

```bash
# Generate secure API keys for your team
export WEBSOCKET_API_KEY_FRONTEND=$(openssl rand -hex 32)
export WEBSOCKET_API_KEY_TRADEBOT=$(openssl rand -hex 32)
export WEBSOCKET_API_KEY_ADMIN=$(openssl rand -hex 32)

# Combine all keys (comma-separated)
export WEBSOCKET_API_KEYS="$WEBSOCKET_API_KEY_FRONTEND,$WEBSOCKET_API_KEY_TRADEBOT,$WEBSOCKET_API_KEY_ADMIN"

echo "Frontend API Key: $WEBSOCKET_API_KEY_FRONTEND"
echo "Trade-bot API Key: $WEBSOCKET_API_KEY_TRADEBOT"
echo "Admin API Key: $WEBSOCKET_API_KEY_ADMIN"
```

### **Step 2: Configure Allowed Origins**

```bash
# Set allowed origins for your application
export ALLOWED_ORIGINS="https://oryntrade.com,https://app.oryntrade.com,http://localhost:3000,http://127.0.0.1:4000"
```

### **Step 3: Deploy with Security**

```bash
# Deploy with security environment variables
cd functions/
export POLYGON_API_KEY="your_polygon_key"
export WEBSOCKET_API_KEYS="key1,key2,key3"
export ALLOWED_ORIGINS="https://oryntrade.com,http://localhost:3000"

./deploy_to_cloud_run.sh
```

## 🔧 Client Configuration

### **Frontend Configuration**

Update `frontend/src/config.js`:

```javascript
// Add API key to WebSocket URL
export const WEBSOCKET_URL = isDevelopment
  ? 'ws://localhost:8081/ws'  // Development (no auth needed)
  : `wss://oryn-websocket-service-xxx.run.app/ws?api_key=${process.env.REACT_APP_WEBSOCKET_API_KEY}`;

// Or set in environment variables
// REACT_APP_WEBSOCKET_API_KEY=your_frontend_api_key
```

### **Trade-bot Configuration**

Update `trade-bot-service/.env`:

```bash
# WebSocket Service with API Key
WEBSOCKET_SERVICE_URL=wss://oryn-websocket-service-xxx.run.app/ws?api_key=your_tradebot_api_key

# Or set User-Agent for automatic authentication
WEBSOCKET_USER_AGENT=oryn-trade-bot/1.0
```

### **Trade-bot Code Update**

Update `trade-bot-service/data/websocket_market_data.py`:

```python
# Add User-Agent header for authentication
headers = {
    'User-Agent': 'oryn-trade-bot/1.0'
}

async with websockets.connect(
    self.websocket_url, 
    extra_headers=headers
) as websocket:
    # ... rest of connection code
```

## 🛡️ Security Best Practices

### **1. API Key Management**
- ✅ **Rotate keys regularly** (monthly/quarterly)
- ✅ **Use different keys** for different clients
- ✅ **Store keys securely** (environment variables, not code)
- ✅ **Monitor key usage** in logs

### **2. Network Security**
- ✅ **Use HTTPS/WSS only** in production
- ✅ **Validate origins** for web clients
- ✅ **Rate limiting** (implemented in Cloud Run)
- ✅ **Monitor connections** and detect anomalies

### **3. Access Control**
- ✅ **Principle of least privilege**
- ✅ **Separate keys for different environments**
- ✅ **Audit access logs regularly**
- ✅ **Revoke compromised keys immediately**

## 📊 Monitoring & Alerting

### **Security Monitoring**

```bash
# Monitor authentication failures
gcloud run logs read --service=oryn-websocket-service \
  --filter="Authentication failed" --limit=100

# Monitor connection patterns
gcloud run logs read --service=oryn-websocket-service \
  --filter="WebSocket connection" --limit=100

# Check for unusual traffic
gcloud monitoring metrics list \
  --filter="resource.type=cloud_run_revision"
```

### **Set Up Alerts**

1. **Failed Authentication Attempts**
   - Alert when > 10 failed attempts per minute
   - Could indicate brute force attack

2. **Unusual Connection Patterns**
   - Alert on connections from unexpected IPs
   - Monitor for connection spikes

3. **API Key Usage**
   - Track which keys are being used
   - Alert on unused or overused keys

## 🚨 Incident Response

### **If API Key is Compromised**

1. **Immediate Actions**:
   ```bash
   # Remove compromised key from environment
   export WEBSOCKET_API_KEYS="key2,key3"  # Remove key1
   
   # Redeploy service
   ./deploy_to_cloud_run.sh
   ```

2. **Generate New Key**:
   ```bash
   # Generate replacement key
   NEW_KEY=$(openssl rand -hex 32)
   export WEBSOCKET_API_KEYS="$NEW_KEY,key2,key3"
   ```

3. **Update Clients**:
   - Update all clients with new API key
   - Test connections before removing old key

### **If Service is Under Attack**

1. **Temporary Lockdown**:
   ```bash
   # Restrict to known origins only
   export ALLOWED_ORIGINS="https://oryntrade.com"
   ./deploy_to_cloud_run.sh
   ```

2. **Scale Down**:
   ```bash
   # Reduce max instances to limit impact
   gcloud run services update oryn-websocket-service \
     --max-instances=2 --region=us-central1
   ```

## 💡 Recommended Security Configuration

For production, use this configuration:

```bash
# Strong API keys (32 bytes each)
export WEBSOCKET_API_KEYS="$(openssl rand -hex 32),$(openssl rand -hex 32),$(openssl rand -hex 32)"

# Restrict origins to your domains only
export ALLOWED_ORIGINS="https://oryntrade.com,https://app.oryntrade.com"

# Deploy with security
./deploy_to_cloud_run.sh
```

This provides multiple layers of security while maintaining usability for your team.
