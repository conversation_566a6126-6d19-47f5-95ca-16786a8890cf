# 🚀 WebSocket Service Production Deployment Guide

This guide covers deploying the Oryn WebSocket service to Google Cloud Run for production use.

## Prerequisites

1. **Google Cloud SDK** installed and authenticated
2. **Docker** installed and running
3. **Polygon.io API Key** 
4. **Google Cloud Project** with billing enabled

## Quick Deployment

### 1. Set Environment Variables

```bash
export GOOGLE_CLOUD_PROJECT="oryntrade"
export POLYGON_API_KEY="your_polygon_api_key_here"
```

### 2. Deploy to Cloud Run

```bash
cd functions/
chmod +x deploy_to_cloud_run.sh
./deploy_to_cloud_run.sh
```

### 3. Update Client Configurations

After deployment, update your client configurations:

#### Frontend Configuration
Update `frontend/src/config.js`:
```javascript
export const USE_FIREBASE_EMULATOR = false; // Set to false for production
```

#### Trade-bot Configuration
Update `trade-bot-service/.env`:
```bash
WEBSOCKET_SERVICE_URL=wss://oryn-websocket-service-ihjc6tjxia-uc.a.run.app/ws
```

## Service Architecture

### Cloud Run Configuration
- **CPU**: 2 vCPU
- **Memory**: 2 GiB
- **Min Instances**: 1 (always warm)
- **Max Instances**: 20 (auto-scaling)
- **Concurrency**: 1000 connections per instance
- **Timeout**: 3600 seconds (1 hour)

### Scaling Strategy
- **Horizontal Scaling**: Up to 20 instances based on CPU/memory usage
- **Connection Distribution**: Load balanced across instances
- **Polygon Connection**: Single connection per instance, shared among clients

## Monitoring & Health Checks

### Health Endpoints
- **Health Check**: `https://your-service-url/health`
- **Status**: `https://your-service-url/status`
- **Metrics**: `https://your-service-url/metrics` (if enabled)

### Monitoring Commands
```bash
# View logs
gcloud run logs tail --service=oryn-websocket-service --region=us-central1

# Check service status
gcloud run services describe oryn-websocket-service --region=us-central1

# Update scaling
gcloud run services update oryn-websocket-service --max-instances=50 --region=us-central1
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ENVIRONMENT` | Environment mode | `production` |
| `POLYGON_API_KEY` | Polygon.io API key | Required |
| `PORT` | Service port | `8080` |
| `MAX_CONNECTIONS` | Max WebSocket connections | `2000` |
| `LOG_LEVEL` | Logging level | `INFO` |

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check if service is running: `gcloud run services list`
   - Verify health endpoint: `curl https://your-service-url/health`

2. **High Latency**
   - Check instance count: May need to increase min-instances
   - Monitor CPU/Memory usage in Cloud Console

3. **WebSocket Disconnections**
   - Check Polygon.io API limits
   - Verify network connectivity
   - Review service logs

### Debug Commands
```bash
# Check service logs
gcloud run logs read --service=oryn-websocket-service --limit=100

# Test WebSocket connection
wscat -c wss://your-service-url/ws

# Monitor resource usage
gcloud monitoring metrics list --filter="resource.type=cloud_run_revision"
```

## Security Considerations

- Service runs with minimal privileges
- API keys passed via environment variables (not in code)
- HTTPS/WSS encryption for all connections
- No persistent data storage (stateless)

## Cost Optimization

- **Min Instances**: Set to 1 for always-warm service
- **CPU Allocation**: Only during request processing
- **Memory**: Right-sized at 2GiB
- **Network**: Pay per GB of data transfer

Estimated cost: ~$20-50/month for moderate usage (1000 connections, 24/7 uptime)
