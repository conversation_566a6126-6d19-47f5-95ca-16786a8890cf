#!/bin/bash

# Set environment variable to fix fork() issue on macOS
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Start Firebase emulator with data import/export
# --import: Load existing data on startup
# --export-on-exit: Save data when stopping emulator
firebase emulators:start --only functions,firestore,storage,auth --project oryntrade --import=./firebase-data --export-on-exit=./firebase-data