#!/usr/bin/env python3
"""
Test script to verify trade bot service compatibility with frontend strategy format.
Tests the new indicator implementations with the exact format generated by the frontend.
"""

import sys
import os
import json
from typing import Dict, List, Any

# Add the trade-bot-service directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from strategies.base_strategy import BaseStrategy

def create_frontend_strategy_format() -> str:
    """Create a strategy JSON in the exact format generated by the frontend."""
    strategy = {
        "name": "Frontend Compatible Strategy",
        "instruments": "EUR/USD",
        "timeframe": "1h",
        "tradingSession": ["London", "New York"],
        "indicators": [
            {
                "id": "macd_1",
                "indicator_class": "MACD",
                "type": "MACD",
                "source": "price",
                "barRef": "close",
                "parameters": {
                    "fast": 12,  # Frontend format
                    "slow": 26,  # Frontend format
                    "signal": 9  # Frontend format
                }
            },
            {
                "id": "bb_1",
                "indicator_class": "BollingerBands",  # Frontend format
                "type": "BollingerBands",
                "source": "price",
                "barRef": "close",
                "parameters": {
                    "period": 20,
                    "devfactor": 2.0  # Frontend format
                }
            },
            {
                "id": "atr_1",
                "indicator_class": "ATR",
                "type": "ATR",
                "source": "price",
                "parameters": {
                    "period": 14,
                    "multiplier": 1.0
                }
            }
        ],
        "entryRules": [
            {
                "id": "entry_1",
                "tradeType": "buy",  # Frontend format
                "indicator1": "macd_1",
                "operator": ">",
                "compareType": "value",  # Frontend format
                "indicator2": "",
                "value": 0,
                "barRef": "close"
            }
        ],
        "exitRules": [
            {
                "id": "exit_1",
                "tradeType": "buy",  # Frontend format
                "indicator1": "macd_1",
                "operator": "<",
                "compareType": "value",  # Frontend format
                "indicator2": "",
                "value": 0,
                "barRef": "close"
            }
        ],
        "riskManagement": {
            # Modern frontend format
            "riskPercentage": "1",
            "riskRewardRatio": "2",
            "stopLossMethod": "fixed",
            "fixedPips": "10",
            "stopLossUnit": "pips",
            "takeProfitUnit": "percentage",
            "maxDailyLoss": "5",
            "maxPositionSize": "10",
            "runtime": 7,
            "totalProfitTarget": "20",
            "totalLossLimit": "10"
        }
    }

    return json.dumps(strategy)

def create_sample_market_data() -> List[Dict[str, Any]]:
    """Create sample market data for testing."""
    market_data = []
    base_price = 1.1000

    for i in range(50):
        # Simple price movement simulation
        price_change = (i % 10 - 5) * 0.0001
        current_price = base_price + price_change

        high = current_price + 0.0005
        low = current_price - 0.0005
        open_price = current_price - 0.0002
        close_price = current_price + 0.0002

        market_data.append({
            "open": open_price,
            "high": high,
            "low": low,
            "close": close_price,
            "volume": 1000 + (i * 10)
        })

    return market_data

def test_frontend_compatibility():
    """Test that the trade bot service can handle frontend strategy format."""
    print("🧪 Testing frontend strategy format compatibility...")

    # Create test data
    market_data = create_sample_market_data()
    strategy_json = create_frontend_strategy_format()

    try:
        # Initialize strategy with frontend format
        print("📊 Initializing strategy with frontend format...")
        strategy = BaseStrategy(strategy_json)
        print(f"✅ Strategy '{strategy.name}' initialized successfully")

        # Test indicator calculations
        print("🔢 Calculating indicators...")
        indicators = strategy.calculate_indicators(market_data)

        # Verify MACD with frontend parameter names
        print("\n📈 Testing MACD with frontend parameters (fast/slow/signal):")
        if "macd_1_macd" in indicators:
            macd_values = [v for v in indicators["macd_1_macd"] if v is not None]
            print(f"  ✅ MACD line: {len(macd_values)} valid values")
            if macd_values:
                print(f"     Last 3 values: {[round(v, 6) for v in macd_values[-3:]]}")

        # Verify Bollinger Bands with frontend parameter names
        print("\n📊 Testing Bollinger Bands with frontend parameters (devfactor):")
        if "bb_1_upper" in indicators:
            upper_values = [v for v in indicators["bb_1_upper"] if v is not None]
            print(f"  ✅ Upper band: {len(upper_values)} valid values")
            if upper_values:
                print(f"     Last 3 values: {[round(v, 4) for v in upper_values[-3:]]}")

        # Verify ATR
        print("\n📉 Testing ATR:")
        if "atr_1" in indicators:
            atr_values = [v for v in indicators["atr_1"] if v is not None]
            print(f"  ✅ ATR: {len(atr_values)} valid values")
            if atr_values:
                print(f"     Last 3 values: {[round(v, 6) for v in atr_values[-3:]]}")

        # Test risk management parsing
        print("\n💰 Testing risk management parsing:")
        print(f"  ✅ Risk percentage: {strategy.risk_percentage}%")
        print(f"  ✅ Stop loss method: {strategy.stop_loss_method}")
        print(f"  ✅ Fixed pips: {strategy.fixed_pips}")

        # Test trading session parsing
        print("\n🕐 Testing trading session parsing:")
        # Parse the strategy JSON to access trading sessions
        strategy_data = json.loads(strategy_json)
        trading_sessions = strategy_data.get("tradingSession", [])
        print(f"  ✅ Trading sessions: {trading_sessions}")

        print(f"\n🎉 Frontend compatibility test completed successfully!")
        print(f"📋 Total indicators calculated: {len(indicators)}")
        print(f"📝 Indicator keys: {list(indicators.keys())}")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting frontend compatibility tests...")
    success = test_frontend_compatibility()

    if success:
        print("\n✅ All tests passed! Trade bot service is compatible with frontend format.")
        sys.exit(0)
    else:
        print("\n❌ Tests failed! Please check the implementation.")
        sys.exit(1)
