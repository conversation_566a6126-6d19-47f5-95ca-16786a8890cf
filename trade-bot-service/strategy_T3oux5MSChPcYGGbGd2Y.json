{"name": "Demo High-Frequency Multi-Test", "description": "Aggressive 5m strategy for testing multiple indicators and large position sizes", "instruments": "EUR/USD", "timeframe": "5m", "tradingSession": ["All"], "indicators": [{"id": "rsi_fast", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 9}, "source": "price"}, {"id": "rsi_slow", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 21}, "source": "price"}, {"id": "ema_fast", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 8}, "source": "price"}, {"id": "ema_slow", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 21}, "source": "price"}, {"id": "bb_main", "type": "BollingerBands", "indicator_class": "BollingerBands", "parameters": {"period": 20, "devfactor": 1.5}, "source": "price"}, {"id": "atr_main", "type": "ATR", "indicator_class": "ATR", "parameters": {"period": 14, "multiplier": 1}, "source": "price"}], "entryRules": [{"tradeType": "long", "indicator1": "rsi_fast", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "40", "logicalOperator": "AND", "barRef": "close", "id": "long_entry_1"}, {"tradeType": "long", "indicator1": "ema_fast", "operator": "Crossing above", "compareType": "indicator", "indicator2": "ema_slow", "value": "", "logicalOperator": "OR", "barRef": "close", "id": "long_entry_2"}, {"tradeType": "short", "indicator1": "rsi_fast", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "60", "logicalOperator": "AND", "barRef": "close", "id": "short_entry_1"}, {"tradeType": "short", "indicator1": "ema_fast", "operator": "Crossing below", "compareType": "indicator", "indicator2": "ema_slow", "value": "", "logicalOperator": "OR", "barRef": "close", "id": "short_entry_2"}], "exitRules": [{"tradeType": "long", "indicator1": "rsi_fast", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "logicalOperator": "OR", "barRef": "close", "id": "long_exit_1"}, {"tradeType": "short", "indicator1": "rsi_fast", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "logicalOperator": "OR", "barRef": "close", "id": "short_exit_1"}], "riskManagement": {"riskPercentage": "10", "riskRewardRatio": "1.5", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "atr", "parameters": {"period": 14, "multiplier": 2}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "user_id": "2tixoSldb3K4Fmz015AZQbTm2QfJ", "id": "T3oux5MSChPcYGGbGd2Y"}