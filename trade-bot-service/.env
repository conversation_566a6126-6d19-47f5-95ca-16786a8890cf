STRATEGY_ID=testStratLocal
USER_ID=m4FNR2Tco5cHREiI575eRkOwrE0g
POLYGON_API_KEY=********************************
OANDA_PRACTICE_MODE=true
USE_FIREBASE_EMULATOR=true  # or true if using emulator

# true if you want to bypass the market is closed check and get candles from a couple days ago
# ONLY USE THIS FOR LOCAL TESTING DURING THE WEEKEND
# TURN OFF BEFORE DEPLOYING
BYPASS_MARKET_IS_CLOSED=false

# WebSocket service configuration for real-time data
# WEBSOCKET_SERVICE_URL=ws://localhost:8081/ws  # Local development
WEBSOCKET_SERVICE_URL=wss://oryn-websocket-service-87400455587.us-central1.run.app/ws  # Production

# WebSocket API Key for production authentication
WEBSOCKET_API_KEY=689963d507a588a391d7f634a8da6e68f4dd755cafbcf234b77ffd541f35a09c